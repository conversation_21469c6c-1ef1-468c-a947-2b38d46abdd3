<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.mq.dao.StbusSaleListTmpMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.mq.entity.StbusSaleListTmpEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="province_id" property="provinceId" jdbcType="BIGINT"/>
        <result column="province_name" property="provinceName" jdbcType="VARCHAR"/>
        <result column="city_id" property="cityId" jdbcType="BIGINT"/>
        <result column="city_name" property="cityName" jdbcType="VARCHAR"/>
        <result column="district_id" property="districtId" jdbcType="BIGINT"/>
        <result column="district_name" property="districtName" jdbcType="VARCHAR"/>
        <result column="market_id" property="marketId" jdbcType="BIGINT"/>
        <result column="market_name" property="marketName" jdbcType="VARCHAR"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="company_name" property="companyName" jdbcType="VARCHAR"/>
        <result column="shop_unique" property="shopUnique" jdbcType="BIGINT"/>
        <result column="shop_name" property="shopName" jdbcType="VARCHAR"/>
        <result column="sale_list_unique" property="saleListUnique" jdbcType="VARCHAR"/>
        <result column="sale_list_datetime" property="saleListDatetime" jdbcType="TIMESTAMP"/>
        <result column="cus_unique" property="cusUnique" jdbcType="VARCHAR"/>
        <result column="customer_name" property="customerName" jdbcType="VARCHAR"/>
        <result column="goods_barcode" property="goodsBarcode" jdbcType="VARCHAR"/>
        <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
        <result column="goods_count" property="goodsCount" jdbcType="DECIMAL"/>
        <result column="sale_amount" property="saleAmount" jdbcType="DECIMAL"/>
        <result column="pay_method" property="payMethod" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_user" property="modifyUser" jdbcType="BIGINT"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `province_id`, `province_name`, `city_id`, `city_name`, `district_id`, 
		`district_name`, `market_id`, `market_name`, `company_id`, `company_name`, `shop_unique`, 
		`shop_name`, `sale_list_unique`, `sale_list_datetime`, `cus_unique`, `customer_name`, `goods_barcode`, `goods_name`, `goods_count`, `sale_amount`, `pay_method`,
		`create_user`, `create_time`, `modify_user`, `modify_time`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="provinceId != null">
            and province_id=#{provinceId}
		</if>
        <if test="provinceName != null  and provinceName != ''">
            and province_name=#{provinceName}
		</if>
        <if test="cityId != null">
            and city_id=#{cityId}
		</if>
        <if test="cityName != null  and cityName != ''">
            and city_name=#{cityName}
		</if>
        <if test="districtId != null">
            and district_id=#{districtId}
		</if>
        <if test="districtName != null  and districtName != ''">
            and district_name=#{districtName}
		</if>
        <if test="marketId != null">
            and market_id=#{marketId}
		</if>
        <if test="marketName != null  and marketName != ''">
            and market_name=#{marketName}
		</if>
        <if test="companyId != null">
            and company_id=#{companyId}
		</if>
        <if test="companyName != null  and companyName != ''">
            and company_name=#{companyName}
		</if>
        <if test="shopUnique != null">
            and shop_unique=#{shopUnique}
		</if>
        <if test="shopName != null  and shopName != ''">
            and shop_name=#{shopName}
		</if>
        <if test="saleListUnique != null  and saleListUnique != ''">
            and sale_list_unique=#{saleListUnique}
		</if>
		<if test="saleListDatetime != null">
            and sale_list_datetime=#{saleListDatetime}
		</if>
		<if test="cusUnique != null  and cusUnique != ''">
            and cus_unique=#{cusUnique}
		</if>
        <if test="customerName != null  and customerName != ''">
            and customer_name=#{customerName}
		</if>
		<if test="goodsBarcode != null  and goodsBarcode != ''">
            and goods_barcode=#{goodsBarcode}
		</if>
        <if test="goodsName != null  and goodsName != ''">
            and goods_name=#{goodsName}
		</if>
		<if test="goodsCount != null">
            and goods_count=#{goodsCount}
		</if>
        <if test="saleAmount != null">
            and sale_amount=#{saleAmount}
		</if>
        <if test="payMethod != null  and payMethod != ''">
            and pay_method=#{payMethod}
		</if>
        <if test="createUser != null">
            and create_user=#{createUser}
		</if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
		</if>
        <if test="modifyUser != null">
            and modify_user=#{modifyUser}
		</if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
		</if>
    </sql>

    <!-- 按条件查询 -->
	<select id="findList" parameterType="cc.buyhoo.tax.mq.entity.StbusSaleListTmpEntity" resultMap="BaseResultMap">
        select <include refid="baseColumns"/> from stbus_sale_list_tmp
        <where>
            <include refid="whereSql"/>
        </where>
	</select>

</mapper>