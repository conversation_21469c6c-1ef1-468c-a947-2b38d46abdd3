<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.mq.dao.SysMigrateMapper">

    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.mq.entity.SysMigrateEntity">
        <result property="id" column="id"/>
        <result property="inCompanyId" column="in_company_id"/>
        <result property="outCompanyId" column="out_company_id"/>
        <result property="shopUnique" column="shop_unique"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="auditContent" column="audit_content"/>
        <result property="auditUser" column="audit_user"/>
        <result property="auditTime" column="audit_time"/>
        <result property="remark" column="remark"/>
        <result property="enableStatus" column="enable_status"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyUser" column="modify_user"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>


    <sql id="baseColumns">
        id,in_company_id,out_company_id,shop_unique,audit_status,audit_content,audit_user,audit_time,remark,enable_status,create_user,create_time,modify_user,modify_time,del_flag
    </sql>
    <sql id="whereSql">
        <if test="id != null ">
            and a.id = #{id},
        </if>
        <if test="inCompanyId != null ">
            and a.in_company_id = #{inCompanyId},
        </if>
        <if test="outCompanyId != null ">
            and a.out_company_id = #{outCompanyId},
        </if>
        <if test="shopUnique != null ">
            and a.shop_unique = #{shopUnique},
        </if>
        <if test="auditStatus != null ">
            and a.audit_status = #{auditStatus},
        </if>
        <if test="auditContent != null  and auditContent.trim() != ''">
            and a.audit_content = #{auditContent},
        </if>
        <if test="auditUser != null ">
            and a.audit_user = #{auditUser},
        </if>
        <if test="auditTime != null ">
            and a.audit_time = #{auditTime},
        </if>
        <if test="remark != null  and remark.trim() != ''">
            and a.remark = #{remark},
        </if>
        <if test="enableStatus != null ">
            and a.enable_status = #{enableStatus},
        </if>
        <if test="createUser != null ">
            and a.create_user = #{createUser},
        </if>
        <if test="createTime != null ">
            and a.create_time = #{createTime},
        </if>
        <if test="modifyUser != null ">
            and a.modify_user = #{modifyUser},
        </if>
        <if test="modifyTime != null ">
            and a.modify_time = #{modifyTime},
        </if>
        <if test="delFlag != null ">
            and a.del_flag = #{delFlag},
        </if>
    </sql>
    <!-- 按条件查询 -->
    <select id="findList" parameterType="cc.buyhoo.tax.mq.entity.SysMigrateEntity" resultMap="BaseResultMap">
        select
        <include refid="baseColumns"/>
        from sys_migrate
        <where>
            <include refid="whereSql"/>
        </where>
    </select>
</mapper>