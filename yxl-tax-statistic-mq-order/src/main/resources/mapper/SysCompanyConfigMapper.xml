<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cc.buyhoo.tax.mq.dao.SysCompanyConfigMapper">
    
    <sql id="baseSql">
        sccf.id,
        sccf.company_id,
        sccf.create_time,
        sccf.modify_time,
        sccf.create_user,
        sccf.modify_user,
        sccf.disassemble_status,
        sccf.disassemble_min_money,
        sccf.disassemble_max_money,
        sccf.disassemble_start_money
    </sql>

    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.mq.entity.SysCompanyConfigEntity">
        <id column="id" property="id"/>
        <result column="company_id" property="companyId"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="create_user" property="createUser"/>
        <result column="modify_user" property="modifyUser"/>
        <result column="disassemble_status" property="disassembleStatus"/>
        <result column="disassemble_min_money" property="disassembleMinMoney"/>
        <result column="disassemble_max_money" property="disassembleMaxMoney"/>
        <result column="disassemble_start_money" property="disassembleStartMoney"/>
        
    </resultMap>
    
    <sql id="whereSql">
        <if test="id != null">
            AND sccf.id = #{id}
        </if>
        <if test="companyId != null">
            AND sccf.company_id = #{companyId}
        </if>
        <if test="createUser != null">
            AND sccf.create_user = #{createUser}
        </if>
        <if test="modifyUser != null">
            AND sccf.modify_user = #{modifyUser}
        </if>
        <if test="disassembleStatus != null">
            AND sccf.disassemble_status = #{disassembleStatus}
        </if>
    </sql>

    <select id="findList" resultMap="BaseResultMap">
        SELECT 
            <include refid="baseSql"/>
        FROM sys_company_config sccf
        <where>
            <include refid="whereSql"/>
        </where>
    </select>

</mapper>