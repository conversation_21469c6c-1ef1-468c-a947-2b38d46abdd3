<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.mq.dao.SysCompanyPayTypeMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.mq.entity.SysCompanyPayTypeEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="mch_id" property="mchId" jdbcType="VARCHAR"/>
        <result column="mch_key" property="mchKey" jdbcType="VARCHAR"/>
        <result column="other_set_wo" property="otherSetWo" jdbcType="VARCHAR"/>
        <result column="other_set" property="otherSet" jdbcType="VARCHAR"/>
        <result column="pay_type" property="payType" jdbcType="INTEGER"/>
        <result column="enable_status" property="enableStatus" jdbcType="INTEGER"/>
        <result column="create_user" property="createUser" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_user" property="modifyUser" jdbcType="BIGINT"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="del_flag" property="delFlag" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `company_id`, `mch_id`, `mch_key`, `other_set_wo`, `other_set`, 
		`pay_type`, `enable_status`, `create_user`, `create_time`, `modify_user`, `modify_time`, `del_flag`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="companyId != null">
            and company_id=#{companyId}
		</if>
        <if test="mchId != null  and mchId != ''">
            and mch_id=#{mchId}
		</if>
        <if test="mchKey != null  and mchKey != ''">
            and mch_key=#{mchKey}
		</if>
        <if test="otherSetWo != null  and otherSetWo != ''">
            and other_set_wo=#{otherSetWo}
		</if>
        <if test="otherSet != null  and otherSet != ''">
            and other_set=#{otherSet}
		</if>
        <if test="payType != null">
            and pay_type=#{payType}
		</if>
        <if test="enableStatus != null">
            and enable_status=#{enableStatus}
		</if>
        <if test="createUser != null">
            and create_user=#{createUser}
		</if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
		</if>
        <if test="modifyUser != null">
            and modify_user=#{modifyUser}
		</if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
		</if>
        <if test="delFlag != null">
            and del_flag=#{delFlag}
		</if>
    </sql>

    <!-- 按条件查询 -->
	<select id="findList" parameterType="cc.buyhoo.tax.mq.entity.SysCompanyPayTypeEntity" resultMap="BaseResultMap">
        select <include refid="baseColumns"/> from sys_company_pay_type
        <where>
            <include refid="whereSql"/>
        </where>
	</select>

</mapper>