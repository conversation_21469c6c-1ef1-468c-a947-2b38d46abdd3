<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.mq.dao.Cnarea2023Mapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.mq.entity.Cnarea2023Entity">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="level" property="level" jdbcType="TINYINT"/>
        <result column="parent_code" property="parentCode" jdbcType="BIGINT"/>
        <result column="area_code" property="areaCode" jdbcType="BIGINT"/>
        <result column="short_parent_code" property="shortParentCode" jdbcType="BIGINT"/>
        <result column="short_area_code" property="shortAreaCode" jdbcType="BIGINT"/>
        <result column="zip_code" property="zipCode" jdbcType="INTEGER"/>
        <result column="city_code" property="cityCode" jdbcType="CHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="short_name" property="shortName" jdbcType="VARCHAR"/>
        <result column="merger_name" property="mergerName" jdbcType="VARCHAR"/>
        <result column="pinyin" property="pinyin" jdbcType="VARCHAR"/>
        <result column="lng" property="lng" jdbcType="DECIMAL"/>
        <result column="lat" property="lat" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `level`, `parent_code`, `area_code`, `short_parent_code`, `short_area_code`, `zip_code`,
		`city_code`, `name`, `short_name`, `merger_name`, `pinyin`, `lng`, `lat`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="level != null">
            and level=#{level}
		</if>
        <if test="parentCode != null">
            and parent_code=#{parentCode}
		</if>
        <if test="areaCode != null">
            and area_code=#{areaCode}
		</if>
		<if test="shortParentCode != null">
            and short_parent_code=#{shortParentCode}
		</if>
        <if test="shortAreaCode != null">
            and short_area_code=#{shortAreaCode}
		</if>
        <if test="zipCode != null">
            and zip_code=#{zipCode}
		</if>
        <if test="cityCode != null  and cityCode != ''">
            and city_code=#{cityCode}
		</if>
        <if test="name != null  and name != ''">
            and name=#{name}
		</if>
        <if test="shortName != null  and shortName != ''">
            and short_name=#{shortName}
		</if>
        <if test="mergerName != null  and mergerName != ''">
            and merger_name=#{mergerName}
		</if>
        <if test="pinyin != null  and pinyin != ''">
            and pinyin=#{pinyin}
		</if>
        <if test="lng != null">
            and lng=#{lng}
		</if>
        <if test="lat != null">
            and lat=#{lat}
		</if>
    </sql>

    <!-- 按条件查询 -->
	<select id="findList" parameterType="cc.buyhoo.tax.mq.entity.Cnarea2023Entity" resultMap="BaseResultMap">
        select <include refid="baseColumns"/> from cnarea_2023
        <where>
            <include refid="whereSql"/>
        </where>
	</select>

</mapper>