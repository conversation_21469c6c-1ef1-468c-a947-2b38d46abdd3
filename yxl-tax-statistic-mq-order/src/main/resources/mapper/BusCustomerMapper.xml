<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.buyhoo.tax.mq.dao.BusCustomerMapper">
    <resultMap id="BaseResultMap" type="cc.buyhoo.tax.mq.entity.BusCustomerEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="cus_unique" property="cusUnique" jdbcType="VARCHAR"/>
        <result column="pc_nick_name" property="pcNickName" jdbcType="VARCHAR"/>
        <result column="pc_sex" property="pcSex" jdbcType="INTEGER"/>
        <result column="pc_birthday" property="pcBirthday" jdbcType="VARCHAR"/>
        <result column="enable_status" property="enableStatus" jdbcType="INTEGER"/>
        <result column="del_flag" property="delFlag" jdbcType="INTEGER"/>
        <result column="create_user" property="createUser" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_user" property="modifyUser" jdbcType="BIGINT"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="baseColumns">
        `id`, `cus_unique`, `pc_nick_name`, `pc_sex`, `pc_birthday`, `enable_status`, 
		`del_flag`, `create_user`, `create_time`, `modify_user`, `modify_time`
    </sql>

    <sql id="whereSql">
        <if test="id != null">
            and id=#{id}
		</if>
        <if test="cusUnique != null  and cusUnique != ''">
            and cus_unique=#{cusUnique}
		</if>
        <if test="pcNickName != null  and pcNickName != ''">
            and pc_nick_name=#{pcNickName}
		</if>
        <if test="pcSex != null">
            and pc_sex=#{pcSex}
		</if>
        <if test="pcBirthday != null  and pcBirthday != ''">
            and pc_birthday=#{pcBirthday}
		</if>
        <if test="enableStatus != null">
            and enable_status=#{enableStatus}
		</if>
        <if test="delFlag != null">
            and del_flag=#{delFlag}
		</if>
        <if test="createUser != null">
            and create_user=#{createUser}
		</if>
        <if test="createTime != null  and createTime != ''">
            and create_time=#{createTime}
		</if>
        <if test="modifyUser != null">
            and modify_user=#{modifyUser}
		</if>
        <if test="modifyTime != null  and modifyTime != ''">
            and modify_time=#{modifyTime}
		</if>
    </sql>

    <!-- 按条件查询 -->
	<select id="findList" parameterType="cc.buyhoo.tax.mq.entity.BusCustomerEntity" resultMap="BaseResultMap">
        select <include refid="baseColumns"/> from bus_customer
        <where>
            <include refid="whereSql"/>
        </where>
	</select>

</mapper>