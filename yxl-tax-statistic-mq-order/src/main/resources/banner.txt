                   _                   _______ _________ _______ __________________ _______ __________________ _______             _______  _______
|\     /||\     /|( \                 (  ____ \\__   __/(  ___  )\__   __/\__   __/(  ____ \\__   __/\__   __/(  ____ \           (       )(  ___  )
( \   / )( \   / )| (                 | (    \/   ) (   | (   ) |   ) (      ) (   | (    \/   ) (      ) (   | (    \/           | () () || (   ) |
 \ (_) /  \ (_) / | |         _____   | (_____    | |   | (___) |   | |      | |   | (_____    | |      | |   | |         _____   | || || || |   | |
  \   /    ) _ (  | |        (_____)  (_____  )   | |   |  ___  |   | |      | |   (_____  )   | |      | |   | |        (_____)  | |(_)| || |   | |
   ) (    / ( ) \ | |                       ) |   | |   | (   ) |   | |      | |         ) |   | |      | |   | |                 | |   | || | /\| |
   | |   ( /   \ )| (____/\           /\____) |   | |   | )   ( |   | |   ___) (___/\____) |   | |   ___) (___| (____/\           | )   ( || (_\ \ |
   \_/   |/     \|(_______/           \_______)   )_(   |/     \|   )_(   \_______/\_______)   )_(   \_______/(_______/           |/     \|(____\/_)

Spring Boot Version: ${spring-boot.version}
Spring Application Name: ${spring.application.name}
Spring Profiles Active: ${spring.profiles.active}
Spring Application Port: ${server.port}
Nacos Server-addr: ${spring.cloud.nacos.server-addr}
Nacos Username: ${spring.cloud.nacos.username}
Nacos Password: ${spring.cloud.nacos.password}
Nacos Group: ${spring.cloud.nacos.config.group}
Nacos Namespace: ${spring.cloud.nacos.config.namespace}

--------------------------------------------------------------------------------------------------------
