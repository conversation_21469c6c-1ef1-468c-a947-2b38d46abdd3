package cc.buyhoo.tax.mq.service.impl;

import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.tax.mq.dao.BusShopMapper;
import cc.buyhoo.tax.mq.entity.BusShopEntity;
import cc.buyhoo.tax.mq.service.BusShopService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * @Description 供应商管理
 * @ClassName BusShopServiceImpl
 * <AUTHOR>
 * @Date 2023/7/26 13:54
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class BusShopServiceImpl implements BusShopService {

    private final BusShopMapper busShopMapper;

    @Override
    public BusShopEntity selectByShopUnique(Long shopUnique) {
        LambdaQueryWrapper<BusShopEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusShopEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        queryWrapper.eq(BusShopEntity::getShopUnique, shopUnique);
        queryWrapper.last("limit 1");
        return busShopMapper.selectOne(queryWrapper);
    }
}
