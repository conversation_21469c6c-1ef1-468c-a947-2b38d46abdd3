package cc.buyhoo.tax.mq.params;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description  mq订单参数
 * @ClassName SaleListSubscribeParams
 * <AUTHOR>
 * @Date 2024/9/5 9:55
 **/
@Data
public class SaleListSubscribeParams implements Serializable {
    private static final long serialVersionUID = 1192330470396210297L;

    /**
     * 订单详情
     */
    private SaleListParams saleListParams;
    /**
     * 订单明细
     */
    private List<SaleListDetailParams> saleListDetailParamsList;

    /**
     * 订单支付记录
     */
    private List<SaleListPayDetailParams> saleListPayDetailParamsList;

    /**
     * 会员信息
     */
    private CustomerParams customerParams;
}
