package cc.buyhoo.tax.mq.enums;

import cc.buyhoo.common.enums.ErrorEnum;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum SysCompanyErrorEnum implements ErrorEnum {

    ID_NULL_ERROR("company-0001","未获取到企业信息"),
    PERMISSION_ERROR("company-0002","系统权限不足，请与管理员联系"),
    INVITATION_CODE_ERROR("company-0003","邀请码重复"),
    NOT_DELETE("company-0004","非自己创建企业禁止删除"),
    NOT_PAY("company-0005","企业未配置支付信息"),
    NOT_PAY_USE("company-0006","无可用支付渠道");

    private final String code;
    private final String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
