package cc.buyhoo.tax.mq.params;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description 订单支付明细
 * @ClassName SaleListPayDetailParams
 * <AUTHOR>
 * @Date 2024/9/5 8:33
 **/
@Data
public class SaleListPayDetailParams implements Serializable {
    private static final long serialVersionUID = 1412604687234523905L;

    /**
     * sale_list_pay_detail_id
     */
    private Integer saleListPayDetailId;

    /**
     * sale_list_unique
     */
    private Long saleListUnique;

    /**
     * 支付方式：1-现金，2-支付宝，3-微信，4-银行卡 ，5-储值卡 ，6-美团外卖，7-饿了么外卖，9-免密支付rn10-积分兑换 ，11-百货豆 12-拉卡拉;13、易通支付;15 银联 16:合利宝
     */
    private Integer payMethod;

    /**
     * 支付金额
     */
    private BigDecimal payMoney;

    /**
     * 现金支付服务端：2、拉卡拉平台； 3 易通 4、微信平台；6、合利宝；7、银联开放平台
     */
    private Integer serverType;

    /**
     * 收款店铺
     */
    private String mchId;
}
