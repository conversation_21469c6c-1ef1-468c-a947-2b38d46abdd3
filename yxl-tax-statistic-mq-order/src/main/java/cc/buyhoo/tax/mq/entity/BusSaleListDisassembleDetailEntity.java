package cc.buyhoo.tax.mq.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
* 
*/
@Data
@TableName(value = "sale_list_disassemble_detail")
public class BusSaleListDisassembleDetailEntity extends BaseEntity {

    /**
     * 企业ID
     */
    private Long companyId;
    /**
    * 详情id
    */
    private Long saleListDetailId;
    /**
    * 销售单唯一标识
    */
    private String saleListUnique;

     /**
    * 商品id
    */
    private Long goodsId;
    /**
    * 商品条形码
    */
    private String goodsBarcode;
    /**
    * 商品名称
    */
    private String goodsName;
    /**
    * 商品数量
    */
    private BigDecimal saleListDetailCount;
    /**
    * 商品购买的价格
    */
    private BigDecimal saleListDetailPrice;
    /**
    * 金额小计
    */
    private BigDecimal saleListDetailSubtotal;

}