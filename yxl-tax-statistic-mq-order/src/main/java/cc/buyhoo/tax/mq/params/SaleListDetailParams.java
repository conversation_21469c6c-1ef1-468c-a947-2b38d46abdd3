package cc.buyhoo.tax.mq.params;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description 订单明细
 * @ClassName SaleListDetailParams
 * <AUTHOR>
 * @Date 2024/9/5 8:33
 **/
@Data
public class SaleListDetailParams implements Serializable {
    private static final long serialVersionUID = -7543173729541480940L;

    /**
     * 销售清单id
     */
    private Integer saleListDetailId;

    /**
     * 销售单唯一标识
     */
    private Long saleListUnique;

    /**
     * 商品条形码
     */
    private String goodsBarcode;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品图片路径
     */
    private String goodsPicturepath;

    /**
     * 商品数量
     */
    private BigDecimal saleListDetailCount;

    /**
     * 商品购买的价格
     */
    private BigDecimal saleListDetailPrice;

    /**
     * 金额小计
     */
    private BigDecimal saleListDetailSubtotal;

    /**
     * 商品id
     */
    private Long goodsId;

    /**
     * 商品进价
     */
    private BigDecimal goodsPurprice;

    /**
     * 提成小计
     */
    private BigDecimal commissionTotal;

    /**
     * goods_old_price
     */
    private BigDecimal goodsOldPrice;

    /**
     * 价格标签
     */
    private String goodsLabel;

    /**
     * 供货商赠送百货豆
     */
    private Integer goodsBeansCount;

    /**
     * 商家赠送百货豆
     */
    private Integer shopBeansCount;

    /**
     * 快递关联id
     */
    private Integer saleListExpressId;
}
