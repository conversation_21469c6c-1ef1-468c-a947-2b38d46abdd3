package cc.buyhoo.tax.mq.entity;


import cc.buyhoo.common.datasource.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@EqualsAndHashCode(callSuper=false)
@Data
@TableName("bus_shop_bill")
public class BusShopBillEntity extends BaseEntity {

    /**
     * 公司编号
     */
    private Long companyId;

    /**
     * 商家唯一标识
     */
    private Long shopUnique;

    /**
     * 已结算金额
     */
    private BigDecimal settledAmount;

    /**
     * 未结算金额
     */
    private BigDecimal unsettledAmount;

    /**
     * 结算中金额
     */
    private BigDecimal settledingAmount;

    /**
     * 结算方式，0-手动结算，1-自动结算
     */
    private String settledType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标记:0未删除1已删除
     */
    private Integer delFlag;

    /**
     * 创建人
     */
    private Long createUser;

    /**
     * 修改人
     */
    private Long modifyUser;
}