package cc.buyhoo.tax.mq.controller;

import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.mq.config.RabbitBuyhooConfig;
import cc.buyhoo.tax.mq.params.SaleListParams;
import cc.buyhoo.tax.mq.params.ShopPayChangeParams;
import cc.buyhoo.tax.mq.service.RabbitMqService;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * RabbitMQ手动消息
 * @ClassName MqCoontroller
 * <AUTHOR>
 * @Date 2024/9/4 10:48
 **/
@RequestMapping("/mq")
@RestController
@RequiredArgsConstructor
public class MqCoontroller {

    private final RabbitMqService rabbitMqService;

    /**
     * 商户切换企业
     * @param params
     * @return
     */
    @PostMapping("/shopPayChange")
    public Result<Void> shopPayChange(@RequestBody ShopPayChangeParams params) {
        return rabbitMqService.shopPayChange(params);
    }

}
