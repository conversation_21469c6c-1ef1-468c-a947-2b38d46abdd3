package cc.buyhoo.tax.mq.thread;

import cc.buyhoo.tax.facade.BusShopBillFacade;
import cc.buyhoo.tax.facade.params.busShopBill.CmbcTransferParams;

public class CmbcTrafThread extends Thread{

    private BusShopBillFacade busShopBillFacade;
    private CmbcTransferParams cmbcTransferParams;

    public BusShopBillFacade getBusShopBillFacade() {
        return busShopBillFacade;
    }

    public void setBusShopBillFacade(BusShopBillFacade busShopBillFacade) {
        this.busShopBillFacade = busShopBillFacade;
    }

    public CmbcTrafThread(BusShopBillFacade busShopBillFacade,CmbcTransferParams cmbcTransferParams) {
        this.busShopBillFacade = busShopBillFacade;
        this.cmbcTransferParams = cmbcTransferParams;
    }

    /**
     * 放在线程里单独执行
     */
    public void run() {
        try {
            busShopBillFacade.cmbcTransfer(cmbcTransferParams);
        } catch (Exception e) {
        }
    }
}
