package cc.buyhoo.tax.mq.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* 最近两天订单信息
*/
@Data
@TableName(value = "stbus_sale_list_tmp")
public class StbusSaleListTmpEntity extends BaseEntity {

    /**
    * 所属省ID
    */
    private Long provinceId;
    /**
    * 所属省
    */
    private String provinceName;
    /**
    * 所属市ID
    */
    private Long cityId;
    /**
    * 所属市
    */
    private String cityName;
    /**
    * 所属区县ID
    */
    private Long districtId;
    /**
    * 所属区县
    */
    private String districtName;
    /**
    * 所属市场ID
    */
    private Long marketId;
    /**
    * 市场名称
    */
    private String marketName;
    /**
    * 所属企业ID
    */
    private Long companyId;
    /**
    * 公司名称
    */
    private String companyName;
    /**
    * 所属商户
    */
    private Long shopUnique;
    /**
    * 店铺名称
    */
    private String shopName;
    /**
    * 订单编号
    */
    private String saleListUnique;
    /**
     * 销售单日期
     */
    private Date saleListDatetime;
    /**
     * 消费者唯一编号
     */
    private String cusUnique;
    /**
    * 会员名
    */
    private String customerName;
    /**
     * 商品条形码
     */
    private String goodsBarcode;
    /**
    * 商品名称
    */
    private String goodsName;
    /**
     * 商品数量
     */
    private BigDecimal goodsCount;
    /**
    * 订单金额
    */
    private BigDecimal saleAmount;
    /**
    * 支付方式
    */
    private String payMethod;
    /**
    * 创建人
    */
    private Long createUser;
    /**
    * 修改人
    */
    private Long modifyUser;

}