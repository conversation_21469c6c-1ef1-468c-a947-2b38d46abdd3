package cc.buyhoo.tax.mq.listener;

import cc.buyhoo.tax.mq.config.RabbitBuyhooConfig;
import cc.buyhoo.tax.mq.params.ReturnListSubscribeParams;
import cc.buyhoo.tax.mq.service.SyncReturnListService;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

/**
 * @Description 百货退款订单订阅
 * @ClassName RetSaleListListener
 * <AUTHOR>
 * @Date 2024/9/7 9:32
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class ReturnListListener {

    private final SyncReturnListService syncReturnListService;
    /**
     * 监听退款订单队列
     * @param json
     */
    @RabbitListener(queues = RabbitBuyhooConfig.RETURN_LIST_QUEUE)
    public void returnListSubscribe(String json) {
        ReturnListSubscribeParams params = JSONUtil.toBean(json, ReturnListSubscribeParams.class);
        syncReturnListService.syncReturnList(params);
    }
}
