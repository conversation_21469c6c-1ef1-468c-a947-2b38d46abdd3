package cc.buyhoo.tax.mq.dao;

import cc.buyhoo.common.datasource.BaseMapperPlus;
import cc.buyhoo.tax.mq.entity.SysCompanyConfigEntity;

import java.util.List;


/**
 * 纳统企业配置表
 */
public interface SysCompanyConfigMapper extends BaseMapperPlus<SysCompanyConfigMapper, SysCompanyConfigEntity> {

    /**
     * 查询满足条件的企业配置信息
     * @param sysCompanyConfigEntity
     * @return
     */
    public List<SysCompanyConfigEntity> findList(SysCompanyConfigEntity sysCompanyConfigEntity);
}
