package cc.buyhoo.tax.mq.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
* 
*/
@Data
@TableName(value = "return_list_paydetail")
public class BusReturnListPaydetailEntity extends BaseEntity {
    /**
     * 企业ID
     */
    private Long companyId;
    /**
    * 订单单号
    */
    private String saleListUnique;
    /**
    * 退货单号，一个订单可退多次，也可以有几种不同的退款方式
    */
    private String retListUnique;
    /**
    * 收银退款的收款方式：1、现金；2、支付宝；3、微信；4、银行卡；5、储值卡；6、其他；7、优惠券；8、百货豆查询时，先判断service_type，在判断pay_type
     * 餐饮支付方式:31现金，32支付，33微信，34银行卡，35储值卡，36积分，37优惠券，38赊欠，39金圈支付
    */
    private Integer payType;
    /**
    * 退款金额
    */
    private BigDecimal payMoney;
    /**
    * 收银现金支付服务端：1、线下操作；2、拉卡拉平台； 3 易通 4、微信平台；5、其他平台 ,6、合利宝，7、云平台
     * 餐饮收款方式：31线下支付，32合利宝支付，33瑞银信支付，34银联云支付，35海科融通，36储值卡支付
    */
    private Integer serviceType;
    /**
    * 退款的账号
    */
    private String mchId;

}