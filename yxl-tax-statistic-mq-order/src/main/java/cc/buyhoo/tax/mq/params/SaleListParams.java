package cc.buyhoo.tax.mq.params;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单参数
 * @ClassName SaleListSubscribeParams
 * <AUTHOR>
 * @Date 2024/9/4 10:44
 **/
@Data
public class SaleListParams implements Serializable {
    private static final long serialVersionUID = 8124122610474227910L;
    /**
     * id
     */
    private Integer saleListId;

    /**
     * 订单编号
     */
    private Long saleListUnique;

    /**
     * 商店唯一标识符
     */
    private Long shopUnique;

    /**
     * 销售单日期
     */
    private Date saleListDatetime;

    /**
     * 应收金额
     */
    private BigDecimal saleListTotal;

    /**
     * 订单进货价
     */
    private BigDecimal saleListPur;

    /**
     * 商品总数量
     */
    private Integer saleListTotalcount;

    /**
     * 消费者唯一编号
     */
    private String cusUnique;

    /**
     * 订单类型（0，实体店销售；1，app订单;2，微信商城小程序;3，网页订单;4，美团外卖订单 5，饿了么外卖rnrn订单，6移动端收银）7:收银端平台会员结算 8:积分兑换
     */
    private Integer saleType;

    /**
     * 收货人姓名
     */
    private String saleListName;

    /**
     * 收货人联系电话
     */
    private String saleListPhone;

    /**
     * 订单送货地址，实体店销售为空，网店按实际填写
     */
    private String saleListAddress;

    /**
     * 外送费
     */
    private BigDecimal saleListDelfee;

    /**
     * 商家补贴配送费
     */
    private BigDecimal shopSubsidyDelfee;

    /**
     * 订单折扣率
     */
    private BigDecimal saleListDiscount;

    /**
     * 付款状态-1货到付款未付款，2网上订单未付款，3已付款 ，4赊账 ，5申请退款 ，6同意退款 7拒rnrn绝退款 8自助收银未付款
     */
    private Integer saleListState;

    /**
     * 发货状态：0-已删除-1无效订单-2待发货-3待收货-4已完成-5已取消-6待评论-7配送单待rnrn确认 8-待付款9-待自提 10-配送异常 11-已核单未发货
     */
    private Integer saleListHandlestate;

    /**
     * 支付方式：1-现金，2-支付宝，3-微信，4-银行卡 ，5-储值卡 ，6-美团外卖，7-饿了么外卖，rnrn8-混合支付，9-免密支付，10-积分兑换 ，11-百货豆 12拉卡拉rn13 易通付款码支付  14金圈聚合码
     */
    private Integer saleListPayment;

    /**
     * 订单备注
     */
    private String saleListRemarks;

    /**
     * 0-注册用户订单，1-游客订单
     */
    private Integer saleListFlag;

    /**
     * 订单签收时间
     */
    private Date receiptDatetime;

    /**
     * 订单发货时间
     */
    private Date sendDatetime;

    /**
     * 订单序号-用于统计记录每天的订单序号
     */
    private Integer saleListNumber;

    /**
     * 收银员id-实体销售查询
     */
    private Integer saleListCashier;

    /**
     * 1：pc已同步；2：pc未同步;
     */
    private Integer saleListSameType;

    /**
     * 订单实际收到金额
     */
    private BigDecimal saleListActuallyReceived;

    /**
     * 机器编号
     */
    private Integer machineNum;

    /**
     * 会员卡支付金额(扩展多种支付方式使用)
     */
    private Integer memberCard;

    /**
     * 订单评分
     */
    private Integer evaluatePoint;

    /**
     * 提成总和
     */
    private BigDecimal commissionSum;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 交易流水号
     */
    private String tradeNo;

    /**
     * 取消订单原因描述
     */
    private String cancleReason;

    /**
     * 退款金额
     */
    private BigDecimal refundMoney;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 退款操作原因(同意退款、拒绝退款原因)
     */
    private String refuntOperateReason;

    /**
     * 会员id
     */
    private Integer cusId;

    /**
     * 纬度
     */
    private BigDecimal addrLatitude;

    /**
     * 经度
     */
    private BigDecimal addrLongitude;

    /**
     * 几种商品
     */
    private Integer goodsKindCount;

    /**
     * 1:送货上门 2:自提
     */
    private Integer shippingMethod;

    /**
     * 积分抵扣金额
     */
    private BigDecimal pointDeduction;

    /**
     * 储值卡抵扣金额
     */
    private BigDecimal cardDeduction;

    /**
     * 收货地址标签
     */
    private String labelVal;

    /**
     * 优惠券优惠金额
     */
    private BigDecimal couponAmount;

    /**
     * 百货豆赠送数量
     */
    private BigDecimal beansGet;

    /**
     * 百货豆使用数量
     */
    private BigDecimal beansUse;

    /**
     * 优惠券id
     */
    private Integer shopCouponId;

    /**
     * 积分抵扣积分(抵扣使用的积分数量)
     */
    private BigDecimal pointVal;

    /**
     * 百货豆抵扣金额
     */
    private BigDecimal beansMoney;

    /**
     * 积分获取
     */
    private BigDecimal pointsGet;

    /**
     * 配送方式，0：自配送 1：美团配送 2:一刻钟配送
     */
    private Integer deliveryType;

    /**
     * 订单取消时间
     */
    private Date cancelTime;

    /**
     * 商品总重量
     */
    private BigDecimal goodsWeight;

    /**
     * 小程序提交formid
     */
    private String formid;

    /**
     * 0:未增加店铺余额 1:已增加
     */
    private Integer addShopBalanceStatus;

    /**
     * 配送异常描述
     */
    private String deliveryError;

    /**
     * 平台赠送商家的百货豆
     */
    private BigDecimal platformShopBeans;

    /**
     * 人脸信息
     */
    private String headImage;

    /**
     * cus_face_token
     */
    private String cusFaceToken;

    /**
     * 供货商赠送的百货豆
     */
    private Integer supGiveBeans;

    /**
     * 平台赠送会员的百货豆
     */
    private Integer platformCusBeans;

    /**
     * 退还差价
     */
    private BigDecimal returnPrice;

    /**
     * 核单员工id
     */
    private Integer verifyStaffId;

    /**
     * 商家线上商品补贴百货豆数量
     */
    private Integer shopGiveBeans;
}
