package cc.buyhoo.tax.mq.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> 
 * @Description 市场管理表
 * @ClassName SysMarketEntity
 * @Date 2024-08-23
 **/
@EqualsAndHashCode(callSuper=false)
@Data
@TableName("sys_market")
public class SysMarketEntity extends BaseEntity {

	/**
	* 市场名称
	*/
	private String marketName;

	/**
	* 所属省ID
	*/
	private Long provinceId;

	/**
	* 所属市ID
	*/
	private Long cityId;

	/**
	* 所属区ID
	*/
	private Long districtId;

	/**
	* 所属地区
	*/
	private String cityInfo;

	/**
	* 详细地址
	*/
	private String address;

	/**
	* 经营模式：0-企业独营，1-企业联营
	*/
	private Integer managementModel;

	/**
	* 负责人
	*/
	private String legalPerson;

	/**
	* 负责人电话
	*/
	private String personMobile;

	/**
	* 企业介绍
	*/
	private String introduction;

	/**
	* 备注
	*/
	private String remark;

	/**
	* 有效状态:1正常0无效
	*/
	private Integer enableStatus;

	/**
	* 创建人
	*/
	private Long createUser;

	/**
	* 修改人
	*/
	private Long modifyUser;

	/**
	* 删除标记:0未删除1已删除
	*/
	private Integer delFlag;

}