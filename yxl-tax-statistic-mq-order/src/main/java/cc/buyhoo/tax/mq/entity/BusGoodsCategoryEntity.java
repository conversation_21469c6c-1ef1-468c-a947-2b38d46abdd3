package cc.buyhoo.tax.mq.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 商品分类
 * <AUTHOR> 
 * @ClassName BusGoodsCategory
 * @Date 2023-07-26
 **/
@EqualsAndHashCode(callSuper=false)
@Data
@TableName("bus_goods_category")
public class BusGoodsCategoryEntity extends BaseEntity {

	/**
	* 所属父类ID
	*/
	private Long parentId;

	/**
	* 祖籍列表
	*/
	private String ancestors;

	/**
	* 类别名称
	*/
	private String categoryName;

	/**
	* 有效状态:1正常0无效
	*/
	private Integer enableStatus;

	/**
	* 所属企业ID
	*/
	private Long companyId;

	/**
	* 备注
	*/
	private String remark;

	/**
	* 创建人ID
	*/
	private Long createUser;


	/**
	* 更新人ID
	*/
	private Long modifyUser;


	/**
	* 删除标记:0未删除1已删除
	*/
	private Integer delFlag;

	/**
	 * 分类类型:1默认分类2普通分类
	 */
	private Integer categoryType;

	/**
	 * 税目
	 */
	private String categoryNo;

	/**
	 * 开票商品名称
	 */
	private String goodsName;

	/**
	 * 开票税率
	 */
	private BigDecimal taxRate;

	/**
	 * 单位
	 */
	private String unit;
}