package cc.buyhoo.tax.mq.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 商户切换支付通道
 * @ClassName ShopPayChangeVO
 * <AUTHOR>
 * @Date 2024/9/6 14:54
 **/
@Data
public class ShopPayChangeVO implements Serializable {

    private static final long serialVersionUID = -9116411342192574855L;

    /**
     * 商户编码
     */
    private Long shopUnique;
    /**
     * 支付渠道类型
     */
    private Integer payType;

    /**
     * 支付商户号
     */
    private String mchId;

    /**
     * 支付商户密钥
     */
    private String mchKey;

    /**
     * 公众号配置ID
     */
    private String otherSetWo;

    /**
     * 其他需要配置的店铺信息
     */
    private String otherSet;
    /**
     * 线下可用方式
     */
    private Integer validType;
    /**
     * 线下默认方式
     */
    private Integer defaultType;
    /**
     * 线上可用方式
     */
    private Integer appValidType;
    /**
     * 线上默认方式
     */
    private Integer appDefaultType;
}
