package cc.buyhoo.tax.mq.config;

import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Description
 * @ClassName SimpleRabbitConfig
 * <AUTHOR>
 * @Date 2024/9/3 16:35
 **/
@Configuration
@ConditionalOnClass(RabbitTemplate.class)
public class RabbitBuyhooConfig {

    /**
     * 订单同步队列
     */
    public static final String SALE_LIST_QUEUE = "sale_list_sync_queue";

    /**
     * 订单同步交换机
     */
    public static final String SALE_LIST_EXCHANGE = "sale_list_sync_exchange";

    /**
     * 订单死信队列
     */
    public static final String SALE_LIST_DEAD_QUEUE = "sale_list_dead_queue";
    /**
     * 订单死信交换机
     */
    public static final String SALE_LIST_DEAD_EXCHANGE = "sale_list_dead_exchange";
    /**
     * 订单死信路由键
     */
    public static final String SALE_LIST_DEAD_ROUTING_KEY = "sale_list_dead_routing_key";

    /**
     * 商户支付改变队列
     */
    public static final String SHOP_PAY_CHANGE_QUEUE = "shop_pay_change_queue";
    /**
     * 商户支付改变交换机
     */
    public static final String SHOP_PAY_CHANGE_EXCHANGE = "shop_pay_change_exchange";

    /**
     * 退款订单同步队列
     */
    public static final String RETURN_LIST_QUEUE = "return_list_sync_queue";

    /**
     * 退款订单同步交换机
     */
    public static final String RETURN_LIST_EXCHANGE = "return_list_sync_exchange";
    @Bean
    public FanoutExchange saleListFanoutExchange() {
        return ExchangeBuilder.fanoutExchange(SALE_LIST_EXCHANGE).build();
    }

    @Bean
    public Queue saleListQueue() {
        return QueueBuilder.durable(SALE_LIST_QUEUE)
                .deadLetterExchange(SALE_LIST_DEAD_EXCHANGE)
                .deadLetterRoutingKey(SALE_LIST_DEAD_ROUTING_KEY)
                .build();
    }

    @Bean
    public Binding bindingSaleList() {
        return BindingBuilder.bind(saleListQueue()).to(saleListFanoutExchange());
    }

    @Bean
    public Exchange deadSaleListExchange() {
        return ExchangeBuilder.directExchange(SALE_LIST_DEAD_EXCHANGE).build();
    }

    @Bean
    public Queue deadSaleListQueue() {
        return QueueBuilder.durable(SALE_LIST_DEAD_QUEUE).build();
    }

    @Bean
    public Binding bindingDealSaleList() {
        return BindingBuilder.bind(deadSaleListQueue()).to(deadSaleListExchange()).with(SALE_LIST_DEAD_ROUTING_KEY).noargs();
    }

    @Bean
    public Queue shopPayChangeQueue() {
        return QueueBuilder.durable(SHOP_PAY_CHANGE_QUEUE).build();
    }

    @Bean
    public FanoutExchange shopPayChangeExchange() {
        return ExchangeBuilder.fanoutExchange(SHOP_PAY_CHANGE_EXCHANGE).build();
    }
    @Bean
    public Binding bindingShopPayChange() {
        return BindingBuilder.bind(shopPayChangeQueue()).to(shopPayChangeExchange());
    }

    @Bean
    public FanoutExchange returnListExchange() {
        return ExchangeBuilder.fanoutExchange(RETURN_LIST_EXCHANGE).build();
    }

    @Bean
    public Queue returnListQueue() {
        return QueueBuilder.durable(RETURN_LIST_QUEUE).build();
    }
    @Bean
    public Binding returnListBinding() {
        return BindingBuilder.bind(returnListQueue()).to(returnListExchange());
    }
}