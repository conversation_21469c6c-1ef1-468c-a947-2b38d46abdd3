package cc.buyhoo.tax.mq.service.impl;

import cc.buyhoo.tax.facade.BusShopBillFacade;
import cc.buyhoo.tax.facade.enums.SaleListPayMethodEnum;
import cc.buyhoo.tax.facade.enums.SaleListProfitStatusEnum;
import cc.buyhoo.common.enums.DelFlagEnum;
import cc.buyhoo.common.enums.EnableStatusEnum;
import cc.buyhoo.common.redis.utils.RedisCache;
import cc.buyhoo.tax.facade.params.busShopBill.CmbcTransferParams;
import cc.buyhoo.tax.mq.dao.DisassembleListMapper;
import cc.buyhoo.tax.mq.enums.DisassembleStatusEnum;
import cc.buyhoo.tax.mq.entity.DisassembleListEntity;
import cc.buyhoo.tax.mq.dao.*;
import cc.buyhoo.tax.mq.entity.*;
import cc.buyhoo.tax.mq.enums.*;
import cc.buyhoo.tax.mq.params.*;
import cc.buyhoo.tax.mq.service.*;
import cc.buyhoo.tax.mq.thread.CmbcTrafThread;
import cc.buyhoo.tax.mq.util.CommonUtil;
import cc.buyhoo.tax.mq.util.ResultUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * @ClassName SyncSaleListServiceImpl
 * <AUTHOR>
 * @Date 2024/9/5 10:10
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class SyncSaleListServiceImpl implements SyncSaleListService {

    private final BusShopService busShopService;
    private final SysCompanyService sysCompanyService;
    private final SysMarketService sysMarketService;
    private final RabbitMqService rabbitMqService;
    private final BusSaleListMapper busSaleListMapper;
    private final BusSaleListDetailMapper busSaleListDetailMapper;
    private final BusSaleListPayDetailMapper busSaleListPayDetailMapper;
    private final BusShopInvoiceSettingMapper busShopInvoiceSettingMapper;
    private final BusGoodsCategoryMapper busGoodsCategoryMapper;
    private final BusShopInvoiceMapper busShopInvoiceMapper;
    private final BusGoodsMapper busGoodsMapper;
    private final BusShopInvoiceDetailMapper busShopInvoiceDetailMapper;
    private final BusShopMapper busShopMapper;
    private final SysMigrateMapper sysMigrateMapper;
    private final Cnarea2023Mapper cnarea2023Mapper;
    private final BusCustomerMapper busCustomerMapper;
    private final StbusSaleListTmpMapper stbusSaleListTmpMapper;
    private final SysCompanyConfigMapper sysCompanyConfigMapper;
    private final BusSaleListDisassembleMapper busSaleListDisassembleMapper;
    private final BusSaleListDisassembleDetailMapper busSaleListDisassembleDetailMapper;
    private final BusSaleListDisassemblePayDetailMapper busSaleListDisassemblePayDetailMapper;
    private final DisassembleListMapper disassembleListMapper;
    private final RedisCache redis;
    private final BusShopBillMapper busShopBillMapper;
    @DubboReference(retries = 0,timeout = 100000)
    private BusShopBillFacade busShopBillFacade;



    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void syncSaleList(long tag, Channel channel, SaleListSubscribeParams params) {
        try {
            if (ObjectUtil.isNull(params)) {
                log.error("------[MQ订单同步]----零售订单同步异常，订单数据为空----------");
                return;
            }
            SaleListParams saleListParams = params.getSaleListParams();
            if (ObjectUtil.isNull(saleListParams)) {
                log.error("------[MQ订单同步]----零售订单同步异常，订单数据为空----------");
                channel.basicAck(tag, false);
                return;
            }
            BusShopEntity shopEntity = busShopService.selectByShopUnique(saleListParams.getShopUnique());
            if (ObjectUtil.isNull(shopEntity)) {
                channel.basicAck(tag, false);
                return;
            }
            /**
             * 增加流程，判断订单是否需要做拆单功能
             * 1、查询拆单设置
             * 2、判断是否需要拆单
             * 3、如果不拆单，存储到数据库，增加parent_list_unique字段
             * 4、如果需要拆单，存储到disassemble_list表中，同步存储到sale_list_disassemble及sale_list_disassemble_detail和sale_list_disassemble_pay_detail中
             * 5、调用拆单功能
             * 6、拆的订单暂时不需要开票，或者在拆单功能里增加开票（开票有点多，建议先不开）
             */

            LambdaQueryWrapper<SysCompanyConfigEntity> sysCompanyConfigQueryWrapper = new LambdaQueryWrapper<>();
            sysCompanyConfigQueryWrapper.eq(SysCompanyConfigEntity::getCompanyId, shopEntity.getCompanyId());
            SysCompanyConfigEntity sysCompanyConfigEntity = sysCompanyConfigMapper.selectOne(sysCompanyConfigQueryWrapper);

            //未设置或者未开启拆单功能，按之前的逻辑处理
            LambdaQueryWrapper<BusSaleListEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BusSaleListEntity::getSaleListUnique, saleListParams.getSaleListUnique());
            queryWrapper.eq(BusSaleListEntity::getSaleListDatetime, saleListParams.getSaleListDatetime());
            List<BusSaleListEntity> busSaleListEntities = busSaleListMapper.selectList(queryWrapper);
            if (ObjectUtil.isNotEmpty(busSaleListEntities)) {
                channel.basicAck(tag, false);
                return;
            }

            SysCompanyEntity companyEntity = sysCompanyService.selectById(shopEntity.getCompanyId());
            BigDecimal payFeeRate = BigDecimal.ZERO;
            if (ObjectUtil.isNotNull(companyEntity) && ObjectUtil.isNotNull(companyEntity.getPayFeeRate())) {
                payFeeRate = companyEntity.getPayFeeRate();
            }
            boolean marketFlag = false;
            Long marketId;
            if (ObjectUtil.isNotEmpty(companyEntity) && ObjectUtil.isNotNull(companyEntity.getMarketId())) {
                SysMarketEntity marketEntity = sysMarketService.selectById(companyEntity.getMarketId());
                if (ObjectUtil.isNotEmpty(marketEntity) && ObjectUtil.equals(EnableStatusEnum.AVAILABLE.getCode(),marketEntity.getEnableStatus())
                        && ObjectUtil.equals(DelFlagEnum.EXISTS.getCode(), marketEntity.getDelFlag())
                        && ObjectUtil.isNotNull(marketEntity.getManagementModel())
                        && ObjectUtil.equals(ManagementModelEnum.MANAGEMENT_MODEL_1.getValue(), marketEntity.getManagementModel())) {
                    marketId = marketEntity.getId();
                    marketFlag = true;
                } else {
                    marketId = null;
                }
            } else {
                marketId = null;
            }
            List<SaleListDetailParams> saleListDetailParamsList = params.getSaleListDetailParamsList();
            List<SaleListPayDetailParams> saleListPayDetailParamsList = params.getSaleListPayDetailParamsList();

            //计算线上支付金额
            BigDecimal onlinePay = saleListPayDetailParamsList.stream().filter(p -> (p.getServerType() != 0 || p.getPayMethod() == 13) && p.getPayMethod() != 17).map(SaleListPayDetailParams::getPayMoney).reduce(BigDecimal.ZERO, BigDecimal::add);

            BusSaleListEntity saleList = new BusSaleListEntity();
            BeanUtil.copyProperties(saleListParams, saleList);
            saleList.setCompanyId(shopEntity.getCompanyId());
            saleList.setShopName(shopEntity.getShopName());
            saleList.setOrderType(String.valueOf(shopEntity.getCooperateType()));
            saleList.setSaleListUnique(String.valueOf(saleListParams.getSaleListUnique()));
            BigDecimal serviceFeeRate = BigDecimal.ZERO;
            if (BigDecimal.ZERO.compareTo(shopEntity.getServiceFeeRate()) == -1) {
                serviceFeeRate = NumberUtil.div(shopEntity.getServiceFeeRate(), BigDecimal.valueOf(100));
            }
            BigDecimal serviceFee = NumberUtil.mul(onlinePay, serviceFeeRate).setScale(2, BigDecimal.ROUND_HALF_UP);
            saleList.setSaleListServiceFee(serviceFee);
            saleList.setPayFee(NumberUtil.div(NumberUtil.mul(onlinePay, payFeeRate), BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP));

            if (BigDecimal.ZERO.compareTo(onlinePay) < 0 && BigDecimal.ZERO.compareTo(saleList.getPayFee()) < 0) {
                // 表示线上支付，线上支付成功 =订单金额*（1-手续费率）
                saleList.setProfitTotal(NumberUtil.sub(onlinePay, saleList.getSaleListServiceFee()));
                saleList.setProfitStatus(SaleListProfitStatusEnum.DONE.getValue());
            }

            List<BusSaleListDetailEntity> detailEntityList = saleListDetailParamsList.stream().map(v -> {
                BusSaleListDetailEntity entity = new BusSaleListDetailEntity();
                BeanUtil.copyProperties(v, entity);
                entity.setSaleListUnique(String.valueOf(v.getSaleListUnique()));
                entity.setSaleListDetailId(Long.valueOf(v.getSaleListDetailId()));
                entity.setCompanyId(shopEntity.getCompanyId());
                return entity;
            }).collect(Collectors.toList());

            List<BusSaleListPayDetailEntity> payDetailEntityList = saleListPayDetailParamsList.stream().map(v -> {
                BusSaleListPayDetailEntity entity = new BusSaleListPayDetailEntity();
                BeanUtil.copyProperties(v, entity);
                entity.setCompanyId(shopEntity.getCompanyId());
                entity.setSaleListUnique(String.valueOf(v.getSaleListUnique()));
                return entity;
            }).collect(Collectors.toList());
            saleList.setParentListUnique(saleListParams.getSaleListUnique().toString());

            /**
             * 需要根据订单是否立即打款，对订单进行处理
             * 1、查询纳统企业设置的信息，sysCompany表的instantTransfer 1、0：立即打款，2：不立即打款
             * 2、不立即打款的，按照旧逻辑处理
             * 3、立即打款的，按照新逻辑处理
             * 3.1、立即打款，将订单设置为已结算状态，防止后续操作
             * 3.2、计算服务费，增加结算中金额
             * 3.3、创建民生银行的转账申请
             * 3.4、增加转账申请记录
             */
            Integer instantTransfer = companyEntity.getInstantTransfer();
            log.info("当前设置的立即转账信息为{}",instantTransfer);
            log.info("是否立即转账{}",instantTransfer == 1);
            if (ObjectUtil.isNotEmpty(instantTransfer) && instantTransfer == 1) {
                //需要立即打款
                saleList.setSettledStatus(1);

                /**
                 * 计算需要打款的金额
                 * 1、计算公式：
                 * 1.1、有服务费的商户：(订单总金额 - 交易手续费) * （1 - 合作商成本占比)
                 * 1.2、无服务费的商户：(总采购成本 - 交易手续费) * （1 - 合作商成本占比)
                 * 1.3、其他商户，订单金额  * 合作商成本占比。结算金额四舍五入。此处有坑，如果是两个订单都是0.005，则都入，所以用减法计算。保留两位有效数字。
                 * 2、增加商户结算中的金额
                 * 3、向民生银行发起转账申请，并记录申请记录
                 */
                Long shopUnique = saleList.getShopUnique();
                BigDecimal totalMoney =  saleList.getSaleListTotal();
                BigDecimal fee = saleList.getSaleListServiceFee();

                Integer hasServiceFee = shopEntity.getHasServiceFee();
                //没有服务费，则直接计算
                if (ObjectUtil.isNotEmpty(hasServiceFee) && ObjectUtil.equals(0, hasServiceFee)) {
                    totalMoney = saleList.getProfitTotal();
                }

                totalMoney = NumberUtil.sub(totalMoney, fee);

                BigDecimal cooperatorCostProportion = shopEntity.getCooperatorCostProportion().divide(new BigDecimal(100), 2 , BigDecimal.ROUND_HALF_UP);

                //分出去的金额
                BigDecimal outMoney = totalMoney.multiply(cooperatorCostProportion).setScale(2, BigDecimal.ROUND_HALF_UP);
                //应该打款的金额
                totalMoney = totalMoney.subtract(outMoney).setScale(2, BigDecimal.ROUND_HALF_UP);

                //创建民生银行转账单，添加转账记录，增加店铺转账中金额
                LambdaQueryWrapper<BusShopBillEntity> lambdaQueryWrapperShop = new LambdaQueryWrapper<>();
                lambdaQueryWrapperShop.eq(BusShopBillEntity::getShopUnique, shopEntity.getShopUnique());
                BusShopBillEntity busShopBillEntity = busShopBillMapper.selectOne(lambdaQueryWrapperShop);

                boolean createBillEntity = false;
                if (ObjectUtil.isNull(busShopBillEntity)) {
                    createBillEntity = true;
                    //当前店铺没有信息，则新增一条
                    busShopBillEntity = new BusShopBillEntity();
                    busShopBillEntity.setCompanyId(shopEntity.getCompanyId());
                    busShopBillEntity.setShopUnique(shopEntity.getShopUnique());
                    busShopBillEntity.setUnsettledAmount(BigDecimal.ZERO);
                    busShopBillEntity.setSettledAmount(BigDecimal.ZERO);
                    busShopBillEntity.setSettledingAmount(BigDecimal.ZERO);
                    busShopBillEntity.setSettledType("1");
                    busShopBillEntity.setDelFlag(0);
                }

                BigDecimal unsettledAmount = busShopBillEntity.getUnsettledAmount();
                unsettledAmount = NumberUtil.add(unsettledAmount, totalMoney);

                busShopBillEntity.setUnsettledAmount(unsettledAmount);
                if (createBillEntity) {
                    busShopBillMapper.insert(busShopBillEntity);
                } else {
                    busShopBillMapper.updateById(busShopBillEntity);
                }

                //转账
                CmbcTransferParams cmbcTransferParams = new CmbcTransferParams();
                cmbcTransferParams.setTransferMoney(totalMoney);
                cmbcTransferParams.setRemark("订单自动结算");
                cmbcTransferParams.setUserId(null);
                cc.buyhoo.tax.facade.params.busShopBill.SysCompanyEntity sysCompanyEntity = new cc.buyhoo.tax.facade.params.busShopBill.SysCompanyEntity();
                BeanUtil.copyProperties(companyEntity, sysCompanyEntity);
                cmbcTransferParams.setCompany(sysCompanyEntity);
                List<cc.buyhoo.tax.facade.params.busShopBill.BusShopBillEntity> bills = new ArrayList<>();

                cc.buyhoo.tax.facade.params.busShopBill.BusShopBillEntity b = new cc.buyhoo.tax.facade.params.busShopBill.BusShopBillEntity();
                BeanUtil.copyProperties(busShopBillEntity, b);
                bills.add(b);

                cmbcTransferParams.setCmbcBill(bills);
                try {
                    //同步订单和打款是两个不同的流程，同步订单只负责增加余额。增加余额后，由打款流程处理后续的操作。
                    CmbcTrafThread cmbcTrafThread = new CmbcTrafThread(busShopBillFacade, cmbcTransferParams);
                    cmbcTrafThread.start();
                } catch (Exception e) {
                    log.info("打款失败");
                }

                //如果分账了，分账的用户也需要打款
                if (outMoney.compareTo(BigDecimal.ZERO) != 0) {
                    LambdaQueryWrapper<BusShopBillEntity> lambdaQueryWrapperSup = new LambdaQueryWrapper<>();
                    lambdaQueryWrapperSup.eq(BusShopBillEntity::getShopUnique, shopEntity.getSupplierNo());
                    BusShopBillEntity busSupBillEntity = busShopBillMapper.selectOne(lambdaQueryWrapperSup);

                    busSupBillEntity.setUnsettledAmount(NumberUtil.add(busSupBillEntity.getUnsettledAmount(), outMoney));
                    busShopBillMapper.updateById(busSupBillEntity);
                }

            }


            busSaleListMapper.insert(saleList);
            if (ObjectUtil.isNotEmpty(detailEntityList)) {
                busSaleListDetailMapper.insertBatch(detailEntityList);
            }
            if (ObjectUtil.isNotEmpty(payDetailEntityList)) {
                busSaleListPayDetailMapper.insertBatch(payDetailEntityList);
            }
            channel.basicAck(tag, false);

            ThreadUtil.execAsync(() -> {
                // 订单对应发票数据
                insertShopInvoice(saleList, detailEntityList);
            });
            if (ObjectUtil.isNotEmpty(params.getCustomerParams())) {
                ThreadUtil.execAsync(() -> {
                    // 同步会员信息
                    syncCustomer(params.getCustomerParams());
                });
            }
            ThreadUtil.execAsync(() -> {
                // 订单临时表-纳统大屏实时订单用
                insertSaleListTmp(companyEntity, saleList, detailEntityList, payDetailEntityList,params.getCustomerParams());
            });
            if (marketFlag) {
                ThreadUtil.execAsync(() -> {
                    // 迁入迁出
                    migrateIo(saleListParams.getShopUnique(),companyEntity.getId(),marketId);
                });
            }
            if (ObjectUtil.isNotNull(sysCompanyConfigEntity) && ObjectUtil.equals(sysCompanyConfigEntity.getDisassembleStatus(), 1)) {
                log.info("订单同步时，当前店铺的拆单设置为{}", sysCompanyConfigEntity);

                //判断订单金额是否在拆单范围内
                if (saleListParams.getSaleListActuallyReceived().compareTo(sysCompanyConfigEntity.getDisassembleStartMoney()) < 0) {
                    System.out.println("订单金额不在拆单范围内" + saleListParams.getSaleListActuallyReceived() + "==" + sysCompanyConfigEntity.getDisassembleStartMoney() + "==" + (saleListParams.getSaleListActuallyReceived().compareTo(sysCompanyConfigEntity.getDisassembleStartMoney())));
                    //不需要拆单
                    return;
                }

                //创建新的拆单任务，并执行
                ThreadUtil.execAsync(() -> {
                    BigDecimal minValue = sysCompanyConfigEntity.getDisassembleMinMoney();
                    BigDecimal maxValue = sysCompanyConfigEntity.getDisassembleMaxMoney();

                    DisassembleListEntity disassembleListEntity = new DisassembleListEntity();
                    disassembleListEntity.setCompanyId(shopEntity.getCompanyId());
                    disassembleListEntity.setCreateTime(DateUtil.date());
                    disassembleListEntity.setCreateUser(null);
                    disassembleListEntity.setOrderAmount(saleListParams.getSaleListActuallyReceived());
                    disassembleListEntity.setSaleListUnique(saleListParams.getSaleListUnique().toString());
                    disassembleListEntity.setDisassembleCount(0);
                    disassembleListEntity.setDisassembleStatus(DisassembleStatusEnum.DISASSEMBLE_ING.getValue());
                    disassembleListEntity.setMinAmountSet(minValue);
                    disassembleListEntity.setMaxAmountSet(maxValue);
                    disassembleListEntity.setMinAmountActual(BigDecimal.ZERO);
                    disassembleListEntity.setMaxAmountActual(BigDecimal.ONE);
                    disassembleListEntity.setDisassmebleRemarks("");
                    disassembleListMapper.insert(disassembleListEntity);

                    ThreadUtil.execAsync(() -> {
                        log.info("创建子订单信息开始");
                        try {
                            disassembleOrderSub(minValue, maxValue, saleListParams.getSaleListActuallyReceived(), saleListParams.getSaleListPur() , saleListParams.getShopUnique(), saleListParams.getSaleListUnique().toString());
                        } catch (Exception e) {
                            log.error("-----[订单拆解异常]-------------处理订单异常：{}-------------------", e);
                            //失败了，修改状态
                            disassembleListEntity.setDisassembleStatus(DisassembleStatusEnum.DISASSEMBLE_FAIL.getValue());
                            disassembleListEntity.setDisassmebleRemarks(e.getMessage());
                            disassembleListMapper.updateById(disassembleListEntity);
                        }
                    });
                });

                channel.basicAck(tag, false);
            }
        } catch (IOException e) {
            log.error("-----[MQ订单同步异常]-------------处理MQ订单异常：{}-------------------", e);
            try {
                channel.basicReject(tag, false);
            } catch (IOException ex) {
                log.error("-----[MQ订单同步异常]-------------消费MQ消息异常：{}-------------------", e);
            }
        }
    }


    public void disassembleOrderSub(BigDecimal minValue, BigDecimal maxValue, BigDecimal totalMoney, BigDecimal proTotal, Long shopUnique, String saleListUnique) {

        log.info("创建订单的参数分别为minValue=" + minValue + ",maxValue=" + maxValue + ",totalMoney=" + totalMoney + ",proTotal=" + proTotal + ",shopUnique=" + shopUnique + ",saleListUnique=" + saleListUnique);

        List<BusSaleListPayDetailEntity> saleListPayDetailEntities = busSaleListPayDetailMapper.selectList(new LambdaQueryWrapper<BusSaleListPayDetailEntity>().eq(BusSaleListPayDetailEntity::getSaleListUnique, saleListUnique));

        LambdaQueryWrapper<BusSaleListEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusSaleListEntity::getShopUnique, shopUnique);
        wrapper.eq(BusSaleListEntity::getSaleListUnique, saleListUnique);
        BusSaleListEntity entity = busSaleListMapper.selectOne(wrapper);

        //支付详情，需要根据当前定的的支付详情处理
        BigDecimal minActualAmount = totalMoney;
        BigDecimal maxActualAmount = BigDecimal.ZERO;

        Integer disassembleCount = 0;
        BigDecimal totalBalance = totalMoney;
        BigDecimal totalProTotal = proTotal;
        while (totalBalance.compareTo(BigDecimal.ZERO) > 0) {
            log.info("当前生成的是第" + disassembleCount + "次");
            disassembleCount += 1;
            //依次创建订单号，并生成对应的订单信息
            //订单金额由最小值和最大值直接随机取值
            BigDecimal saleListTotal;
            //如果不能取到随机值，则随机值为0
            if (maxValue.subtract(minValue).compareTo(BigDecimal.ONE) < 0) {
                saleListTotal = maxValue;
                log.info("订单取用最大值{}", saleListTotal);
            } else {
                Integer ran = RandomUtil.randomInt(maxValue.subtract(minValue).intValue());
                saleListTotal = minValue.add(new BigDecimal(ran));
                log.info("订单取用随机值{}", saleListTotal);
            }
            BigDecimal saleListPayTotal = proTotal.multiply(saleListTotal).divide(totalMoney, 2, BigDecimal.ROUND_HALF_UP);
            if (totalBalance.compareTo(saleListTotal) < 0) {
                saleListTotal = totalBalance;
                saleListPayTotal = totalProTotal;
                log.info("订单取用订单余额{}", saleListTotal);
            }
            String saleListNewOrderno = CommonUtil.createOrder(redis);

            List<BusSaleListPayDetailEntity> subPayDetails = new ArrayList<>();
            //根据原订单的支付信息，创建新订单的支付信息
            BigDecimal saleListTotalBalance = saleListTotal;
            while (saleListTotalBalance.compareTo(BigDecimal.ZERO) > 0) {
                for (Integer i = 0; i < saleListPayDetailEntities.size(); i++) {
                    if (saleListPayDetailEntities.get(i).getPayMoney().compareTo(BigDecimal.ZERO) <= 0) {
                        //如果当前支付方式的余额为0，跳过
                        continue;
                    }
                    if (saleListPayDetailEntities.get(i).getPayMoney().compareTo(saleListTotalBalance) >=0 ) {
                        BusSaleListPayDetailEntity payDetailEntity = new BusSaleListPayDetailEntity();
                        BeanUtil.copyProperties(saleListPayDetailEntities.get(i), payDetailEntity);
                        payDetailEntity.setSaleListUnique(saleListNewOrderno);
                        payDetailEntity.setPayMoney(saleListTotalBalance);
                        saleListPayDetailEntities.get(i).setPayMoney(saleListPayDetailEntities.get(i).getPayMoney().subtract(saleListTotalBalance).setScale(2,BigDecimal.ROUND_HALF_UP));
                        payDetailEntity.setId(null);
                        subPayDetails.add(payDetailEntity);
                        saleListTotalBalance = BigDecimal.ZERO;
                        break;
                    } else {
                        //扣除部分余额，继续下一次
                        BusSaleListPayDetailEntity payDetailEntity = new BusSaleListPayDetailEntity();
                        BeanUtil.copyProperties(saleListPayDetailEntities.get(i), payDetailEntity);
                        payDetailEntity.setSaleListUnique(saleListNewOrderno);
                        saleListPayDetailEntities.get(i).setPayMoney(BigDecimal.ZERO);
                        payDetailEntity.setId(null);
                        subPayDetails.add(payDetailEntity);
                        saleListTotalBalance = saleListTotalBalance.subtract(payDetailEntity.getPayMoney());
                    }
                }
            }
            log.info("本次创建订单的金额" + saleListTotal);
            //创建订单信息
            ResultUtil resultUtil = createOrderSigle(saleListUnique, saleListNewOrderno, saleListTotal, saleListPayTotal, shopUnique, subPayDetails);
            //只有成功，才减少余额，并进行下一次循环
            if (resultUtil.isFlag()) {
                totalBalance = totalBalance.subtract(saleListTotal).setScale(2, BigDecimal.ROUND_HALF_UP);
                totalProTotal = totalProTotal.subtract(saleListPayTotal).setScale(2, BigDecimal.ROUND_HALF_UP);
                log.info("本次创建订单的金额" + saleListTotal);
                log.info("本次订单结束后，剩余可用金额" + totalBalance);

                if (minActualAmount.compareTo(saleListTotal) > 0) {
                    minActualAmount = saleListTotal;
                }

                if (maxActualAmount.compareTo(saleListTotal) < 0) {
                    maxActualAmount = saleListTotal;
                }
                continue;
            }
            throw new RuntimeException(resultUtil.getMsg());
        }

        //所有订单拆单完成，更新拆单任务状态
        DisassembleListEntity disassembleListEntity = disassembleListMapper.selectOne(new LambdaQueryWrapper<DisassembleListEntity>().eq(DisassembleListEntity::getSaleListUnique, saleListUnique));
        disassembleListEntity.setDisassembleStatus(DisassembleStatusEnum.DISASSEMBLE_SUCCESS.getValue());
        disassembleListEntity.setMinAmountActual(minActualAmount);
        disassembleListEntity.setMaxAmountActual(maxActualAmount);
        disassembleListEntity.setDisassembleCount(disassembleCount);

        //将订单信息存储到分解表
        List<BusSaleListDetailEntity> saleListDetailEntities = busSaleListDetailMapper.selectList(new LambdaQueryWrapper<BusSaleListDetailEntity>().eq(BusSaleListDetailEntity::getSaleListUnique, saleListUnique));

        //删除原本的数据，添加数据到分解表
        busSaleListMapper.deleteById(entity);
        busSaleListDetailMapper.deleteBatchIds(saleListDetailEntities.stream().map(BusSaleListDetailEntity::getId).collect(Collectors.toList()));
        busSaleListPayDetailMapper.deleteBatchIds(saleListPayDetailEntities.stream().map(BusSaleListPayDetailEntity::getId).collect(Collectors.toList()));


        List<BusSaleListDisassembleDetailEntity> saleListDisassembleDetailEntities = new ArrayList<>();
        List<BusSaleListDisassemblePayDetailEntity> saleListDisassemblePayDetailEntities = new ArrayList<>();

        saleListDisassembleDetailEntities = saleListDetailEntities.stream().map(v -> {
            BusSaleListDisassembleDetailEntity disassembleDetailEntity = new BusSaleListDisassembleDetailEntity();
            BeanUtil.copyProperties(v,disassembleDetailEntity);
            return disassembleDetailEntity;
        }).collect(Collectors.toList());

        saleListDisassemblePayDetailEntities = saleListPayDetailEntities.stream().map(v -> {
            BusSaleListDisassemblePayDetailEntity disassemblePayDetailEntity = new BusSaleListDisassemblePayDetailEntity();
            BeanUtil.copyProperties(v,disassemblePayDetailEntity);
            return disassemblePayDetailEntity;
        }).collect(Collectors.toList());

        BusSaleListDisassembleEntity disassembleEntity = new BusSaleListDisassembleEntity();
        BeanUtil.copyProperties(entity,disassembleEntity);
        busSaleListDisassembleMapper.insert(disassembleEntity);
        busSaleListDisassembleDetailMapper.insertBatch(saleListDisassembleDetailEntities);
        busSaleListDisassemblePayDetailMapper.insertBatch(saleListDisassemblePayDetailEntities);


        LambdaUpdateWrapper<DisassembleListEntity> disassembleListEntityLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        disassembleListEntityLambdaUpdateWrapper.eq(DisassembleListEntity::getSaleListUnique, disassembleListEntity.getSaleListUnique());
        disassembleListMapper.update(disassembleListEntity, disassembleListEntityLambdaUpdateWrapper);
    }

    //创建单条记录
    public ResultUtil createOrderSigle(String oldSaleListUnique, String saleListUnique, BigDecimal saleListTotal, BigDecimal saleListPrototal, Long shopUnique, List<BusSaleListPayDetailEntity> subPayDetails) {
        //校验该订单号是否已存在，防止重复创建
        ResultUtil resultUtil = new ResultUtil();
        LambdaQueryWrapper<BusSaleListEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusSaleListEntity::getSaleListUnique, saleListUnique);
        queryWrapper.eq(BusSaleListEntity::getShopUnique, shopUnique);
        BusSaleListEntity saleList = busSaleListMapper.selectOne(queryWrapper);
        if (ObjectUtil.isNotNull(saleList)) {
            //订单号已存在，创建订单信息失败
            resultUtil.setFlag(false);
            resultUtil.setMsg("订单号已存在，创建订单信息失败");
            return resultUtil;
        }

        LambdaQueryWrapper<BusSaleListEntity> busaSaleListQuery = new LambdaQueryWrapper<>();
        busaSaleListQuery.eq(BusSaleListEntity::getSaleListUnique, oldSaleListUnique);
        busaSaleListQuery.eq(BusSaleListEntity::getShopUnique, shopUnique);
        BusSaleListEntity saleListDisassemble = busSaleListMapper.selectOne(busaSaleListQuery);

        if (ObjectUtil.isNull(saleListDisassemble)) {
            //创建订单信息失败
            resultUtil.setFlag(false);
            resultUtil.setMsg("拆单信息不存在，请重新拆单");
            return resultUtil;
        }

        //筛选出满足条件的商品信息
        List<BusSaleListDetailEntity> detailEntities = queryGoodsListForOrderByPrice(saleListUnique, saleListTotal,shopUnique);
        log.info("筛选的商品信息  detailEntities:{}" + detailEntities + ObjectUtil.isEmpty(detailEntities));
        if (ObjectUtil.isEmpty(detailEntities)) {
            //创建订单信息失败
            resultUtil.setFlag(false);
            resultUtil.setMsg("没有满足条件的商品信息，创建订单信息失败");
            return resultUtil;
        }
        //统计商品总销售金额,修改订单详情的小计
        BigDecimal goodsMoney = BigDecimal.ZERO;
        for (BusSaleListDetailEntity d: detailEntities) {
            BigDecimal subTotal = d.getSaleListDetailPrice().multiply(d.getSaleListDetailCount()).setScale(2,BigDecimal.ROUND_HALF_UP);
            goodsMoney = goodsMoney.add(subTotal).setScale(2,BigDecimal.ROUND_HALF_UP);
            d.setSaleListDetailSubtotal(subTotal);
        }

        //店铺信息
        BusShopEntity shopEntity = busShopMapper.selectOne(new LambdaQueryWrapper<BusShopEntity>().eq(BusShopEntity::getShopUnique,shopUnique));

        //创建订单信息
        BusSaleListEntity saleListEntity = new BusSaleListEntity();
        BeanUtil.copyProperties(saleListDisassemble,saleListEntity);
        saleListEntity.setCompanyId(shopEntity.getCompanyId());
        saleListEntity.setShopUnique(shopEntity.getShopUnique());
        saleListEntity.setShopName(shopEntity.getShopName());
        saleListEntity.setSaleListUnique(saleListUnique);
        saleListEntity.setSaleListTotal(goodsMoney);
        saleListEntity.setParentListUnique(saleListDisassemble.getSaleListUnique());
        saleListEntity.setSaleListActuallyReceived(goodsMoney);
        saleListEntity.setProfitTotal(saleListPrototal);

        //支付详情
        BusSaleListPayDetailEntity payDetail = new BusSaleListPayDetailEntity();
        payDetail.setSaleListUnique(saleListUnique);
        payDetail.setCompanyId(shopEntity.getCompanyId());
        payDetail.setPayMethod(saleListDisassemble.getSaleListPayment());
        payDetail.setPayMoney(goodsMoney);
        payDetail.setServerType(7);
        payDetail.setMchId(null);
        payDetail.setPayTime(DateUtil.date());
        payDetail.setCreateTime(DateUtil.date());
        //保存订单，订单详情，订单支付详情，支付详情应由上一级传过来


        //保存订单，保存订单详情
        saleListEntity.setId(null);
        busSaleListMapper.insert(saleListEntity);
        busSaleListDetailMapper.insertBatch(detailEntities);
        busSaleListPayDetailMapper.insertBatch( subPayDetails);

        resultUtil.setFlag(true);
        return resultUtil;
    }



    public List<BusSaleListDetailEntity> queryGoodsListForOrderByPrice(String saleListUnique, BigDecimal saleListTotal, Long shopUnique) {

        LambdaQueryWrapper<BusShopEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(BusShopEntity::getShopUnique, shopUnique);
        BusShopEntity shopEntity = busShopMapper.selectOne(lambdaQueryWrapper);
        if (ObjectUtil.isEmpty(shopEntity)) {
            //店铺信息不存在，返回错误
            return new ArrayList<>();
        }
        //店铺信息存在，查询店铺内满足条件的商品列表
        LambdaQueryWrapper<BusGoodsEntity> goodsQueryWrapper = new LambdaQueryWrapper<>();
        goodsQueryWrapper.eq(BusGoodsEntity::getShopUnique, shopEntity.getShopUnique());

        List<BusGoodsEntity> goodsList = busGoodsMapper.selectList(goodsQueryWrapper);

        if (ObjectUtil.isEmpty(goodsList)) {
            //商品信息不存在，返回错误
            return new ArrayList<>();
        }

        //从当前商品中选择满足添加的商品信息，如果是最后一单，则修改最后的商品价格数据

        List<BusSaleListDetailEntity> detailEntities = new ArrayList<>();

        //商品数量由1-5直接随机生成
        Integer maxCount = RandomUtil.randomInt(4) + 1;

        detailEntities = chooseGoodsMsgByPrice(detailEntities,saleListTotal,goodsList,maxCount,saleListUnique);

        return detailEntities;
    }

    //筛选商品信息
    public static List<BusSaleListDetailEntity> chooseGoodsMsgByPrice(List<BusSaleListDetailEntity> detailEntities, BigDecimal saleListTotal, List<BusGoodsEntity> goodsList , Integer maxCount, String saleListUnique) {
        BigDecimal disparities = BigDecimal.ZERO;
        BusSaleListDetailEntity detailEntity = new BusSaleListDetailEntity();
        BusGoodsEntity goodsEntity = new BusGoodsEntity();
        if (maxCount <= 0 || saleListTotal.compareTo(BigDecimal.ZERO) <= 0) {
            return detailEntities;
        }
        if (maxCount == 1) {
            goodsEntity = goodsList.get(0);
            disparities = goodsList.get(0).getGoodsSalePrice();
            //如果是最后一个商品，则修找到一个价格最相近的商品，修改商品的销售价格
            for (BusGoodsEntity goods : goodsList) {
                if (goods.getGoodsSalePrice().compareTo(saleListTotal) == 0) {
                    goodsEntity = goods;
                    break;
                }
                if (disparities.compareTo(goods.getGoodsSalePrice().subtract(saleListTotal).abs()) > 0) {
                    //原差价大于最新商品的差价，将最新商品的信息赋值到临时差距
                    disparities = goods.getGoodsSalePrice().subtract(saleListTotal).abs();
                    goodsEntity = goods;
                }
            }

            //获取到最接近的差值，修改商品的销售价格
            //如果差价为0，则直接复制商品，否则，修改商品价格
            if (disparities.compareTo(BigDecimal.ZERO) != 0) {
                goodsEntity.setGoodsSalePrice(saleListTotal);
            }

            //如果存在相同的商品，则修改数量，如果没有，则增加新记录
            Boolean flag = false;
            String goodsBarcode = goodsEntity.getGoodsBarcode();
            for (BusSaleListDetailEntity d : detailEntities) {
                //防止同一个商品，价格不一样
                if (goodsBarcode.equals(d.getGoodsBarcode()) && (goodsEntity.getGoodsSalePrice().compareTo(d.getSaleListDetailPrice()) == 0)) {
                    d.setSaleListDetailCount(d.getSaleListDetailCount().add(BigDecimal.ONE));
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                //将筛选的商品添加到数组中
                BeanUtil.copyProperties(goodsEntity, detailEntity);
                detailEntity.setId(null);
                detailEntity.setSaleListDetailPrice(goodsEntity.getGoodsSalePrice());
                detailEntity.setSaleListDetailCount(BigDecimal.ONE);
                detailEntity.setSaleListUnique(saleListUnique);
                detailEntity.setCreateTime(DateUtil.date());
                detailEntities.add(detailEntity);
            }
            return detailEntities;
        } else {
            //如果是多个商品，则递归调用
            //从商品列表里随机筛选一个金额小于商品总额的商品
            BigDecimal tempSaleListTotal = saleListTotal;
            List<BusGoodsEntity> smallGoodsList = goodsList.stream().filter(v -> {
                if (v.getGoodsSalePrice().compareTo(tempSaleListTotal) <= 0) {
                    return true;
                }
                return false;
            }).collect(Collectors.toList());

            //从商品列表里随机选择一个商品
            if (ObjectUtil.isNotEmpty(smallGoodsList)) {
                Integer index = RandomUtil.randomInt(smallGoodsList.size());
                goodsEntity = smallGoodsList.get(index);
            }
        }

        if (ObjectUtil.isNull(goodsEntity)) {
            return detailEntities;
        }
        log.info("当前选择的商品为goodsEntity:{}",goodsEntity);
        //如果存在相同的商品，则修改数量，如果没有，则增加新记录
        Boolean flag = false;
        String goodsBarcode = goodsEntity.getGoodsBarcode();
        for (BusSaleListDetailEntity d : detailEntities) {
            if (goodsBarcode.equals(d.getGoodsBarcode())) {
                d.setSaleListDetailCount(d.getSaleListDetailCount().add(BigDecimal.ONE));
                saleListTotal = saleListTotal.subtract(goodsEntity.getGoodsSalePrice());
                flag = true;
                break;
            }
        }
        //将数据添加到返回数组中
        if (!flag) {
            //将筛选的商品添加到数组中
            BeanUtil.copyProperties(goodsEntity, detailEntity);
            detailEntity.setId(null);
            detailEntity.setSaleListDetailPrice(goodsEntity.getGoodsSalePrice());
            detailEntity.setSaleListDetailCount(BigDecimal.ONE);
            detailEntity.setSaleListUnique(saleListUnique);
            detailEntity.setCreateTime(DateUtil.date());
            detailEntities.add(detailEntity);
            log.info("当前筛选商品的余额为{}",saleListTotal);
            saleListTotal = saleListTotal.subtract(goodsEntity.getGoodsSalePrice());
        }
        return chooseGoodsMsgByPrice(detailEntities, saleListTotal, goodsList , --maxCount, saleListUnique);
    }
    /**
     * 根据订单生成发票数据
     * @param saleList
     * @param saleListDetailEntityList
     */
    private void insertShopInvoice(BusSaleListEntity saleList, List<BusSaleListDetailEntity> saleListDetailEntityList) {
        BusShopInvoiceSettingEntity settingEntity = busShopInvoiceSettingMapper.selectOne(new LambdaQueryWrapper<BusShopInvoiceSettingEntity>().eq(BusShopInvoiceSettingEntity::getCompanyId, saleList.getCompanyId()).eq(BusShopInvoiceSettingEntity::getPeriodsLevel, PeriodNumberEnum.PERIOD_NUMBER_FOUR.getPeriodsLevel()));
        LambdaQueryWrapper<BusGoodsCategoryEntity> categoryWrapper = new LambdaQueryWrapper<>();
        categoryWrapper.eq(BusGoodsCategoryEntity::getCompanyId, saleList.getCompanyId());
        categoryWrapper.eq(BusGoodsCategoryEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        categoryWrapper.eq(BusGoodsCategoryEntity::getEnableStatus, EnableStatusEnum.AVAILABLE.getCode());
        categoryWrapper.eq(BusGoodsCategoryEntity::getParentId, 0);
        List<BusGoodsCategoryEntity> goodsCategoryEntityList = busGoodsCategoryMapper.selectList(categoryWrapper);
        Map<Long, BusGoodsCategoryEntity> categoryMap = goodsCategoryEntityList.stream().collect(Collectors.toMap(BusGoodsCategoryEntity::getId, v -> v));

        LambdaQueryWrapper<BusShopInvoiceEntity> shopInvoiceWrapper = new LambdaQueryWrapper<>();
        shopInvoiceWrapper.eq(BusShopInvoiceEntity::getCompanyId, saleList.getCompanyId());
        shopInvoiceWrapper.isNotNull(BusShopInvoiceEntity::getShopUnique);
        shopInvoiceWrapper.isNotNull(BusShopInvoiceEntity::getSaleListUnique);
        shopInvoiceWrapper.select(BusShopInvoiceEntity::getShopUnique,BusShopInvoiceEntity::getSaleListUnique);
        List<BusShopInvoiceEntity> invoiceEntityList = busShopInvoiceMapper.selectList(shopInvoiceWrapper);

        LambdaQueryWrapper<BusGoodsEntity> goodsWrapper = new LambdaQueryWrapper<>();
        goodsWrapper.select(BusGoodsEntity::getShopUnique, BusGoodsEntity::getGoodsBarcode, BusGoodsEntity::getCategoryId);
        goodsWrapper.eq(BusGoodsEntity::getCompanyId, saleList.getCompanyId());
        goodsWrapper.eq(BusGoodsEntity::getDelFlag, DelFlagEnum.EXISTS.getCode());
        List<BusGoodsEntity> goodsEntityList = busGoodsMapper.selectList(goodsWrapper);
        Map<String, Long> goodsMap = goodsEntityList.stream().collect(Collectors.toMap(k -> StrUtil.concat(true, String.valueOf(k.getShopUnique()), "_", k.getGoodsBarcode()), BusGoodsEntity::getCategoryId));


        BusShopInvoiceEntity invoiceEntity = new BusShopInvoiceEntity();
        invoiceEntity.setCompanyId(saleList.getCompanyId());
        invoiceEntity.setSaleListUnique(saleList.getSaleListUnique());
        invoiceEntity.setShopUnique(saleList.getShopUnique());
        invoiceEntity.setInvoiceType(2); //发票类型:1进项票2销项票
        invoiceEntity.setMediumType(1); //发票介质：1、电子发票；2、纸质发票
        invoiceEntity.setInvoiceKind(1); //发票种类:1：普通发票；2、专用发票
        invoiceEntity.setPurchaseType(1);//采购方类型：1、个人；2、企业
        invoiceEntity.setStatus(InvoiceStatusEnum.INVOICE_STATUS_2.getValue());
        if (ObjectUtil.isNotNull(settingEntity)) {
            invoiceEntity.setSaleName(settingEntity.getCompanyName());
            invoiceEntity.setSaleIdentity(settingEntity.getCompanyTaxNo());
            invoiceEntity.setSaleAddress(settingEntity.getCompanyAddress());
            invoiceEntity.setSalePhone(settingEntity.getCompanyPhone());
            invoiceEntity.setSaleBank(settingEntity.getInvoiceBankName());
            invoiceEntity.setSaleBankNo(settingEntity.getInvoiceBankCard());
            invoiceEntity.setPayee(settingEntity.getPayee());
            invoiceEntity.setChecker(settingEntity.getChecker());
            invoiceEntity.setInvoiceMan(settingEntity.getInvoiceMan());
            invoiceEntity.setStatus(InvoiceStatusEnum.INVOICE_STATUS_2.getValue());
        }
        BigDecimal taxMoney = BigDecimal.ZERO; //合计税额
        BigDecimal orderTaxMoney = BigDecimal.ZERO; //价税合计
        List<BusShopInvoiceDetailEntity> detailEntityList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(saleListDetailEntityList)) {
            for (BusSaleListDetailEntity sld : saleListDetailEntityList) {
                BusShopInvoiceDetailEntity detailEntity = new BusShopInvoiceDetailEntity();
                detailEntity.setGoodsName(sld.getGoodsName());
                detailEntity.setPrice(sld.getSaleListDetailPrice());
                detailEntity.setQuantity(sld.getSaleListDetailCount());
                detailEntity.setAmount(NumberUtil.mul(detailEntity.getPrice(), detailEntity.getQuantity()).setScale(2, RoundingMode.HALF_DOWN));
                BigDecimal taxRate = BigDecimal.ZERO;
                Long categoryId = goodsMap.get(saleList.getShopUnique() + "_" + sld.getGoodsBarcode());
                BusGoodsCategoryEntity categoryEntity = categoryMap.get(categoryId);
                if (null != categoryEntity) {
                    taxRate = NumberUtil.null2Zero(categoryEntity.getTaxRate());
                    detailEntity.setTaxClassificationCode(categoryEntity.getCategoryNo());
                    detailEntity.setTaxClassificationName(categoryEntity.getGoodsName());
                    detailEntity.setUnit(categoryEntity.getUnit());
                }
                if (BigDecimal.ZERO.compareTo(taxRate) == -1) {
                    taxRate = NumberUtil.div(taxRate, 100);
                }
                detailEntity.setTaxRate(taxRate);
                detailEntity.setTaxAmount(NumberUtil.mul(NumberUtil.div(detailEntity.getAmount(), NumberUtil.add(BigDecimal.ONE, taxRate)), taxRate).setScale(2, BigDecimal.ROUND_HALF_DOWN));
                detailEntityList.add(detailEntity);

                taxMoney = NumberUtil.add(taxMoney, detailEntity.getTaxAmount());
                orderTaxMoney = NumberUtil.add(orderTaxMoney, detailEntity.getAmount());
            }
            // 处理订单总金额 与 明细总金额不一致问题
            if (saleList.getSaleListActuallyReceived().compareTo(orderTaxMoney) == 1) {
                //订单总金额 大于 明细总金额
                BigDecimal diffV = NumberUtil.sub(saleList.getSaleListActuallyReceived(), orderTaxMoney);
                BusShopInvoiceDetailEntity detailEntity = detailEntityList.get(detailEntityList.size() - 1);
                detailEntity.setAmount(NumberUtil.add(detailEntity.getAmount(), diffV));
                detailEntity.setPrice(NumberUtil.div(detailEntity.getAmount(), detailEntity.getQuantity()).setScale(12, RoundingMode.HALF_UP));
                detailEntity.setTaxAmount(NumberUtil.mul(NumberUtil.div(detailEntity.getAmount(), NumberUtil.add(BigDecimal.ONE, detailEntity.getTaxRate())), detailEntity.getTaxRate()).setScale(2, BigDecimal.ROUND_HALF_DOWN));
                detailEntityList.remove(detailEntityList.size() - 1);
                detailEntityList.add(detailEntity);
            } else if (saleList.getSaleListActuallyReceived().compareTo(orderTaxMoney) == -1) {
                //订单总金额 小于 明细总金额
                BigDecimal diffV = NumberUtil.sub(orderTaxMoney, saleList.getSaleListActuallyReceived());
                BusShopInvoiceDetailEntity detailEntity = detailEntityList.get(detailEntityList.size() - 1);
                if (diffV.compareTo(detailEntity.getAmount()) == -1) {
                    detailEntity.setAmount(NumberUtil.sub(detailEntity.getAmount(), diffV));
                    detailEntity.setPrice(NumberUtil.div(detailEntity.getAmount(), detailEntity.getQuantity()).setScale(12, RoundingMode.HALF_UP));
                    detailEntity.setTaxAmount(NumberUtil.mul(NumberUtil.div(detailEntity.getAmount(), NumberUtil.add(BigDecimal.ONE, detailEntity.getTaxRate())), detailEntity.getTaxRate()).setScale(2, BigDecimal.ROUND_HALF_DOWN));
                    detailEntityList.remove(detailEntityList.size() - 1);
                    detailEntityList.add(detailEntity);
                }
            }
        }
        invoiceEntity.setOrderMoney(NumberUtil.sub(saleList.getSaleListActuallyReceived(), taxMoney));
        invoiceEntity.setTaxMoney(taxMoney);
        invoiceEntity.setOrderTaxMoney(saleList.getSaleListActuallyReceived());
        if (ObjectUtil.isEmpty(invoiceEntityList) || (ObjectUtil.isNotEmpty(invoiceEntityList)
                && invoiceEntityList.stream().noneMatch(v -> v.getSaleListUnique().equals(saleList.getSaleListUnique())
                && v.getShopUnique().equals(saleList.getShopUnique())))) {
            int n = busShopInvoiceMapper.insert(invoiceEntity);
            if (n > 0 && CollectionUtil.isNotEmpty(detailEntityList)) {
                detailEntityList.stream().forEach(v -> v.setInvoiceId(invoiceEntity.getId()));
                busShopInvoiceDetailMapper.insertBatch(detailEntityList);
            }
        }
    }

    private void migrateIo(Long shopUnique, Long companyId, Long marketId) {
        QueryWrapper<BusSaleListEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BusSaleListEntity::getShopUnique, shopUnique);
        queryWrapper.lambda().eq(BusSaleListEntity::getOrderType, SaleListOrderTypeEnum.ORDER_TYPE_2.getCode());
        queryWrapper.lambda().ge(BusSaleListEntity::getSaleListDatetime, DateUtil.beginOfMonth(DateUtil.date()));
        queryWrapper.select("ROUND(IFNULL(sum(sale_list_actually_received),0),2) AS saleListActuallyReceivedSum");
        List<Map<String, Object>> list = busSaleListMapper.selectMaps(queryWrapper);
        if (ObjectUtil.isNotEmpty(list)) {
            BigDecimal amount = new BigDecimal(String.valueOf(list.get(0).get("saleListActuallyReceivedSum")));
            if (amount.compareTo(BigDecimal.valueOf(95000)) >= 0) {
                LambdaQueryWrapper<BusShopEntity> shopQueryWrapper = new LambdaQueryWrapper<>();
                shopQueryWrapper.eq(BusShopEntity::getShopUnique, shopUnique);
                BusShopEntity shopEntity = busShopMapper.selectOne(shopQueryWrapper);
                if (ObjectUtil.isNotEmpty(shopEntity)) {
                    LambdaQueryWrapper<SysCompanyEntity> sysCompanyQueryWrapper = new LambdaQueryWrapper<>();
                    sysCompanyQueryWrapper.eq(SysCompanyEntity::getMarketId, marketId);
                    sysCompanyQueryWrapper.eq(SysCompanyEntity::getEnableStatus, EnableStatusEnum.AVAILABLE.getCode());
                    sysCompanyQueryWrapper.eq(SysCompanyEntity::getDelFlag,DelFlagEnum.EXISTS.getCode());
                    sysCompanyQueryWrapper.select(SysCompanyEntity::getId, SysCompanyEntity::getStatisticGrade,SysCompanyEntity::getStatisticStatus);
                    sysCompanyQueryWrapper.orderByAsc(SysCompanyEntity::getStatisticGrade);
                    List<SysCompanyEntity> sysCompanyList = sysCompanyService.selectList(sysCompanyQueryWrapper);
                    Long migrateCompanyId = null;
                    if (ObjectUtil.isNotEmpty(sysCompanyList)) {
                        boolean statisticGradeFlag = false;
                        for (SysCompanyEntity sysCompanyEntity : sysCompanyList) {
                            if (ObjectUtil.isNotNull(sysCompanyEntity.getStatisticGrade()) && ObjectUtil.equals(companyId, sysCompanyEntity.getId())) {
                                statisticGradeFlag = true;
                            } else if (ObjectUtil.isNotNull(sysCompanyEntity.getStatisticGrade()) && statisticGradeFlag
                                    && ObjectUtil.equals(SysCompanyStatisticStatusEnum.STATUS_0.getValue(),sysCompanyEntity.getStatisticStatus())) {
                                migrateCompanyId = sysCompanyEntity.getId();
                                break;
                            }
                        }
                    }
                    if (ObjectUtil.isNotEmpty(migrateCompanyId)) {
                        SysMigrateEntity sysMigrateEntity = new SysMigrateEntity();
                        sysMigrateEntity.setShopUnique(shopUnique);
                        sysMigrateEntity.setOutCompanyId(shopEntity.getCompanyId());
                        shopEntity.setModifyTime(DateUtil.date());
                        shopEntity.setCooperateType(BusShopCooperateTypeEnum.COOPERATE_TYPE_1.getValue());
                        shopEntity.setCompanyId(migrateCompanyId);
                        busShopMapper.updateById(shopEntity);

                        sysMigrateEntity.setInCompanyId(migrateCompanyId);
                        sysMigrateEntity.setAuditStatus(SysMigrateAuditStatusEnm.AUDIT_SUCCESS.getValue());
                        sysMigrateEntity.setAuditTime(DateUtil.date());
                        sysMigrateMapper.insert(sysMigrateEntity);

                        ShopPayChangeParams shopPayChangeParams = new ShopPayChangeParams();
                        shopPayChangeParams.setShopUnique(shopUnique);
                        shopPayChangeParams.setCompanyId(migrateCompanyId);
                        rabbitMqService.shopPayChange(shopPayChangeParams);
                    }
                }
            }
        }
    }

    /**
     * 更新会员信息
     * @param customerParams
     */
    private void syncCustomer(CustomerParams customerParams) {
        if (ObjectUtil.isNotEmpty(customerParams.getCusUnique())) {
            LambdaQueryWrapper<BusCustomerEntity> busCustomerQueryWrapper = new LambdaQueryWrapper<>();
            busCustomerQueryWrapper.eq(BusCustomerEntity::getCusUnique, customerParams.getCusUnique());
            List<BusCustomerEntity> busCustomerEntityList = busCustomerMapper.selectList(busCustomerQueryWrapper);
            if (ObjectUtil.isNotEmpty(busCustomerEntityList)) {
                BusCustomerEntity busCustomerEntity = busCustomerEntityList.get(0);
                busCustomerEntity.setPcNickName(customerParams.getPcNickName());
                busCustomerEntity.setPcSex(customerParams.getPcSex());
                busCustomerEntity.setPcBirthday(customerParams.getPcBirthday());
                busCustomerMapper.updateById(busCustomerEntity);
            } else {
                BusCustomerEntity busCustomerEntity = new BusCustomerEntity();
                busCustomerEntity.setCusUnique(customerParams.getCusUnique());
                busCustomerEntity.setPcNickName(customerParams.getPcNickName());
                busCustomerEntity.setPcSex(customerParams.getPcSex());
                busCustomerEntity.setPcBirthday(customerParams.getPcBirthday());
                busCustomerMapper.insert(busCustomerEntity);
            }
        }
    }
    /**
     * 将订单信息插入到订单临时表中，给纳统大屏中实时订单用
     * @param companyEntity
     * @param saleList
     * @param detailEntityList
     * @param payDetailEntityList
     */
    private void insertSaleListTmp(SysCompanyEntity companyEntity, BusSaleListEntity saleList,List<BusSaleListDetailEntity> detailEntityList,List<BusSaleListPayDetailEntity> payDetailEntityList,
                                   CustomerParams customerParams) {
        StbusSaleListTmpEntity saleListTmpEntity = new StbusSaleListTmpEntity();
        if (ObjectUtil.isNotEmpty(saleList)) {
            // 订单信息
            saleListTmpEntity.setSaleListUnique(saleList.getSaleListUnique());
            saleListTmpEntity.setSaleListDatetime(saleList.getSaleListDatetime());
            saleListTmpEntity.setSaleAmount(saleList.getSaleListActuallyReceived());
            saleListTmpEntity.setShopUnique(saleList.getShopUnique());
            saleListTmpEntity.setShopName(saleList.getShopName());
            saleListTmpEntity.setGoodsName("临时商品");
            saleListTmpEntity.setCusUnique(saleList.getCusUnique());
            saleListTmpEntity.setCustomerName("匿名会员");
            saleListTmpEntity.setPayMethod("现金");
            // 支付方式
            if (ObjectUtil.isNotEmpty(saleList.getSaleListPayment())) {
                saleListTmpEntity.setPayMethod(SaleListPayMethodEnum.getValue(saleList.getSaleListPayment()));
            }
            //所属信息和位置信息
            if (ObjectUtil.isNotEmpty(companyEntity)) {
                saleListTmpEntity.setCompanyId(saleList.getCompanyId());
                saleListTmpEntity.setCompanyName(companyEntity.getCompanyName());

                if (ObjectUtil.isNotNull(companyEntity.getMarketId())) {
                    SysMarketEntity sysMarketEntity = sysMarketService.selectById(companyEntity.getMarketId());
                    if (ObjectUtil.isNotEmpty(sysMarketEntity)) {
                        saleListTmpEntity.setMarketId(companyEntity.getMarketId());
                        saleListTmpEntity.setMarketName(sysMarketEntity.getMarketName());

                        LambdaQueryWrapper<Cnarea2023Entity> areaQueryWrapper = new LambdaQueryWrapper<>();
                        areaQueryWrapper.in(Cnarea2023Entity::getLevel, Arrays.asList(1, 2, 3));
                        List<Cnarea2023Entity> areaList = cnarea2023Mapper.selectList(areaQueryWrapper);

                        if (ObjectUtil.isNotNull(sysMarketEntity.getProvinceId()) && ObjectUtil.isNotEmpty(areaList)
                                && ObjectUtil.isNotEmpty(areaList.stream().anyMatch(v -> ObjectUtil.equals(sysMarketEntity.getProvinceId(), v.getShortAreaCode())))) {
                            saleListTmpEntity.setProvinceId(sysMarketEntity.getProvinceId());
                            saleListTmpEntity.setProvinceName(areaList.stream().filter(v -> ObjectUtil.equals(sysMarketEntity.getProvinceId(), v.getShortAreaCode())).findFirst().get().getName());
                        }
                        if (ObjectUtil.isNotNull(sysMarketEntity.getCityId()) && ObjectUtil.isNotEmpty(areaList)
                                && ObjectUtil.isNotEmpty(areaList.stream().anyMatch(v -> ObjectUtil.equals(sysMarketEntity.getCityId(), v.getShortAreaCode())))) {
                            saleListTmpEntity.setCityId(sysMarketEntity.getCityId());
                            saleListTmpEntity.setCityName(areaList.stream().filter(v -> ObjectUtil.equals(sysMarketEntity.getCityId(), v.getShortAreaCode())).findFirst().get().getName());
                        }
                        if (ObjectUtil.isNotNull(sysMarketEntity.getCityId()) && ObjectUtil.isNotEmpty(areaList)
                                && ObjectUtil.isNotEmpty(areaList.stream().anyMatch(v -> ObjectUtil.equals(sysMarketEntity.getDistrictId(), v.getShortAreaCode())))) {
                            saleListTmpEntity.setDistrictId(sysMarketEntity.getDistrictId());
                            saleListTmpEntity.setDistrictName(areaList.stream().filter(v -> ObjectUtil.equals(sysMarketEntity.getDistrictId(), v.getShortAreaCode())).findFirst().get().getName());
                        }
                    }
                }
            }
            //商品信息
            if (ObjectUtil.isNotEmpty(detailEntityList)) {
                BusSaleListDetailEntity detailEntity = detailEntityList.get(0);
                saleListTmpEntity.setGoodsBarcode(detailEntity.getGoodsBarcode());
                String goodsName = detailEntity.getGoodsName();
                if (ObjectUtil.isNotNull(goodsName)) {
                    saleListTmpEntity.setGoodsName(detailEntity.getGoodsName());
                }
                if (ObjectUtil.isNotNull(detailEntity.getSaleListDetailCount())) {
                    saleListTmpEntity.setGoodsCount(detailEntity.getSaleListDetailCount());
                }
            }
            //会员信息
            if (ObjectUtil.isNotNull(saleList.getCusUnique()) && ObjectUtil.isNotEmpty(customerParams) && ObjectUtil.isNotEmpty(customerParams.getPcNickName())) {
                saleListTmpEntity.setCustomerName(customerParams.getPcNickName());
            }
            stbusSaleListTmpMapper.insert(saleListTmpEntity);
        }
    }
}
