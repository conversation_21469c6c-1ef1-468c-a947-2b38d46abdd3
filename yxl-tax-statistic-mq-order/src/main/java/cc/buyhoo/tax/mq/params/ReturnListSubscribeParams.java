package cc.buyhoo.tax.mq.params;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description MQ退款订单
 * @ClassName RetSaleListSubscribeParams
 * <AUTHOR>
 * @Date 2024/9/7 9:43
 **/
@Data
public class ReturnListSubscribeParams implements Serializable {
    /**
     * 退款订单
     */
    private ReturnListParams returnListParams;
    /**
     * 退款商品明细
     */
    private List<ReturnListDetailParams> detailParamsList;
    /**
     * 退款支付明细
     */
    private List<ReturnListPayDetailParams> payDetailParamsList;
}
