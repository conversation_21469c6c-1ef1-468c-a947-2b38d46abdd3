package cc.buyhoo.tax.mq.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
* 中国行政地区表
*/
@Data
@TableName(value = "cnarea_2023")
public class Cnarea2023Entity {

    /**
     * id
     */
    private Long id;
    /**
    * 层级
    */
    private Integer level;
    /**
    * 父级行政代码
    */
    private Long parentCode;
    /**
    * 行政代码
    */
    private Long areaCode;
    /**
     * 父级行政代码简称
     */
    private Long shortParentCode;
    /**
    * 行政区简写
    */
    private Long shortAreaCode;
    /**
    * 邮政编码
    */
    private String zipCode;
    /**
    * 区号
    */
    private String cityCode;
    /**
    * 名称
    */
    private String name;
    /**
    * 简称
    */
    private String shortName;
    /**
    * 组合名
    */
    private String mergerName;
    /**
    * 拼音
    */
    private String pinyin;
    /**
    * 经度
    */
    private BigDecimal lng;
    /**
    * 纬度
    */
    private BigDecimal lat;

}