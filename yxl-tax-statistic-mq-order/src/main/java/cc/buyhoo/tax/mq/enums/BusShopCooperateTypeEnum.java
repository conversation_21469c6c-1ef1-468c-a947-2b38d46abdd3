package cc.buyhoo.tax.mq.enums;

import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description 订单类型枚举
 * @ClassName SaleListOrderTypeEnum
 * <AUTHOR>
 * @Date 2024/8/5 20:21
 **/
@Getter
@AllArgsConstructor
public enum BusShopCooperateTypeEnum {

    COOPERATE_TYPE_1(1, "代收代付"),
    COOPERATE_TYPE_2(2, "联营");

    private Integer value;
    private String name;
    public static String getName(Integer value) {
        for (BusShopCooperateTypeEnum e : BusShopCooperateTypeEnum.values()) {
            if (ObjectUtil.equals(e.getValue(), value)) {
                return e.getName();
            }
        }
        return null;
    }
}
