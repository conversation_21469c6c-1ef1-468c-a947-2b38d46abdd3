package cc.buyhoo.tax.mq.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* 店铺发票明细
*/
@Data
@TableName(value = "bus_shop_invoice")
public class BusShopInvoiceEntity extends BaseEntity {

    /**
    * 公司编号
    */
    private Long companyId;
    /**
    * 店铺编号
    */
    private Long shopUnique;
    /**
     * 订单编号
     */
    private String saleListUnique;
    /**
    * 发票类型:1进项票2销项票
    */
    private Integer invoiceType;
    /**
    * 发票代码
    */
    private String invoiceCode;
    /**
    * 机器编码
    */
    private String machineCode;
    /**
    * 发票号码
    */
    private String invoiceNumber;
    /**
     * 发票介质：1、电子发票；2、纸质发票
     */
    private Integer mediumType;
    /**
    * 发票种类:1普通发票2专用发票
    */
    private Integer invoiceKind;
    /**
    * 开票日期
    */
    private Date invoiceDate;
    /**
    * 校验码
    */
    private String checkCode;
    /**
     * 购买方类型：1、个人；2、企业（个人之需要提供抬头，或默认抬头内容为个人即可）
     */
    private Integer purchaseType;
    /**
    * 购买方名称
    */
    private String purchaseName;
    /**
    * 购买方纳税人识别号
    */
    private String purchaseIdentity;
    /**
    * 购买方地址
    */
    private String purchaseAddress;
    /**
    * 购买方电话
    */
    private String purchasePhone;
    /**
    * 购买方开户行
    */
    private String purchaseBank;
    /**
    * 购买方开户行账号
    */
    private String purchaseBankNo;
    /**
    * 销售方名称
    */
    private String saleName;
    /**
    * 销售方纳税人识别号
    */
    private String saleIdentity;
    /**
    * 销售方地址
    */
    private String saleAddress;
    /**
    * 销售方电话
    */
    private String salePhone;
    /**
    * 销售方开户行
    */
    private String saleBank;
    /**
    * 销售方开户行账号
    */
    private String saleBankNo;
    /**
    * 合计订单金额
    */
    private BigDecimal orderMoney;
    /**
    * 合计税额
    */
    private BigDecimal taxMoney;
    /**
    * 价税合计
    */
    private BigDecimal orderTaxMoney;
    /**
    * 收款人
    */
    private String payee;
    /**
    * 复核人
    */
    private String checker;
    /**
    * 开票人
    */
    private String invoiceMan;
    /**
     * 是否开票：1已开票2未开票；3、开票中；4、开票失败
     */
    private Integer status;
    /**
     * 创建人
     */
    private Long createUser;
    /**
     * 修改人
     */
    private Long modifyUser;
    /**
     * 发牌保存完整地址(4期）
     */
    private String imageUrl;
    /**
     * 发票申请号（4期）
     */
    private String billNumber;

    /**
     * 期数等级
     */
    private Integer periodsLevel;

    /**
     * 接收发票的邮箱或者手机号
     */
    private String receiveMsg;

    /**
     * 备注
     */
    private String notes;

    /**
     * 是否展示购方地址、电话信息：1-是；2-否
     */
    private Integer purchasePersonFlag;

    /**
     * 是否展示销方账号信息：1-是；2-否
     */
    private Integer salePersonFlag;
    /**
     * 是否展示购方地址、电话信息：1-是；2-否
     */
    private Integer purchaseBankFlag;

    /**
     * 是否展示销方账号信息：1-是；2-否
     */
    private Integer saleBankFlag;
    /**
     *是否申请开票：0-否1-是
     */
    private Integer applyFlag;
}