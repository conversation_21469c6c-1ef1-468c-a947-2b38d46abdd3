package cc.buyhoo.tax.mq.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;

/**
 * <AUTHOR> 
 * @Description MQ订单同步异常记录
 * @ClassName BusMqErrorRecord
 * @Date 2024-09-05
 **/
@EqualsAndHashCode(callSuper=false)
@Data
@TableName("bus_mq_error_record")
public class BusMqErrorRecordEntity extends BaseEntity {

	/**
	* 店铺编码
	*/
	private String shopUnique;

	/**
	* 订单编码
	*/
	private String saleListUnique;

	/**
	* 备注
	*/
	private String remark;

	/**
	* 删除标记:0未删除1已删除
	*/
	private Integer delFlag;

	/**
	* 创建人
	*/
	private Long createUser;

	/**
	* 修改人
	*/
	private Long modifyUser;


}