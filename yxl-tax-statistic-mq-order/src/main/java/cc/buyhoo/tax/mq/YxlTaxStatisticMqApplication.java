package cc.buyhoo.tax.mq;

import cn.hutool.crypto.SecureUtil;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * @Description 订单同步
 * @ClassName YxlTaxStatisticMqApplication
 * <AUTHOR>
 * @Date 2024/9/5 8:41
 **/
@SpringBootApplication
@MapperScan("cc.buyhoo.tax.mq.dao")
@EnableDiscoveryClient
@EnableDubbo
public class YxlTaxStatisticMqApplication {

    public static void main(String[] args) {
        SpringApplication.run(YxlTaxStatisticMqApplication.class, args);
        SecureUtil.disableBouncyCastle();
    }

    static {
        //不展示pagehelper.banner
        System.setProperty("pagehelper.banner", "false");
        //同一台服务器部署多个环境，dubbo缓存文件重复问题
        System.setProperty("user.home", "dubboCache");
    }
}
