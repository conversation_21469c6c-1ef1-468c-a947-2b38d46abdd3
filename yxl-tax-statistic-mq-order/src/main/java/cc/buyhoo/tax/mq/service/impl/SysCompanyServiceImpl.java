package cc.buyhoo.tax.mq.service.impl;

import cc.buyhoo.tax.mq.dao.SysCompanyMapper;
import cc.buyhoo.tax.mq.entity.SysCompanyEntity;
import cc.buyhoo.tax.mq.service.SysCompanyService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class SysCompanyServiceImpl implements SysCompanyService {
    private final SysCompanyMapper sysCompanyMapper;

    @Override
    public SysCompanyEntity selectById(Long id) {
        return sysCompanyMapper.selectById(id);
    }

    @Override
    public List<SysCompanyEntity> selectList(LambdaQueryWrapper<SysCompanyEntity> sysCompanyEntityLambdaQueryWrapper) {
        return sysCompanyMapper.selectList(sysCompanyEntityLambdaQueryWrapper);
    }
}
