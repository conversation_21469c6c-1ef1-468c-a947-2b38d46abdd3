package cc.buyhoo.tax.mq.listener;

import cc.buyhoo.tax.mq.config.RabbitBuyhooConfig;
import cc.buyhoo.tax.mq.entity.BusMqErrorRecordEntity;
import cc.buyhoo.tax.mq.params.SaleListParams;
import cc.buyhoo.tax.mq.params.SaleListSubscribeParams;
import cc.buyhoo.tax.mq.service.BusMqErrorRecordService;
import cc.buyhoo.tax.mq.service.SyncSaleListService;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * @Description 百货订单订阅
 * @ClassName SaleListListener
 * <AUTHOR>
 * @Date 2024/9/4 10:33
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class SaleListListener {
    private final SyncSaleListService syncSaleListService;
    private final BusMqErrorRecordService busMqErrorRecordService;

    /**
     * 监听订单队列
     * @param tag
     * @param channel
     * @param json
     */
    @RabbitListener(queues = RabbitBuyhooConfig.SALE_LIST_QUEUE, ackMode = "MANUAL")
    public void saleListSubscribe(@Header(AmqpHeaders.DELIVERY_TAG) long tag, Channel channel, String json) {
        try {
            if (StrUtil.isNotBlank(json)) {
                SaleListSubscribeParams params = JSONUtil.toBean(json, SaleListSubscribeParams.class);
                syncSaleListService.syncSaleList(tag, channel, params);
            } else {
                log.error("-----[MQ订单同步异常]-------------接收到消息为空-------------------");
                channel.basicAck(tag, false);
            }
        } catch (IOException e) {
            log.error("-----[MQ订单同步异常]-------------消费MQ消息异常：{}-------------------", e);
        }
    }

    /**
     * 监听异常队列，保存异常订单
     * @param json
     */
    @RabbitListener(queues = RabbitBuyhooConfig.SALE_LIST_DEAD_QUEUE)
    public void deadSaleListSubscribe(String json) {
        log.info("-----[MQ订单同步:异常订单]------接收订单--------{}-------------", json);
        SaleListSubscribeParams params = JSONUtil.toBean(json, SaleListSubscribeParams.class);
        SaleListParams saleListParams = params.getSaleListParams();
        if (ObjectUtil.isNotNull(saleListParams)) {
            BusMqErrorRecordEntity entity = new BusMqErrorRecordEntity();
            entity.setShopUnique(StrUtil.toString(saleListParams.getShopUnique()));
            entity.setSaleListUnique(StrUtil.toString(saleListParams.getSaleListUnique()));
            entity.setCreateTime(DateUtil.date());
            busMqErrorRecordService.insert(entity);
        }
    }
}
