package cc.buyhoo.tax.mq.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;

/**
* 
*/
@Data
@TableName(value = "bus_customer")
public class BusCustomerEntity extends BaseEntity {

    /**
    * 平台唯一编号
    */
    private String cusUnique;
    /**
    * 昵称
    */
    private String pcNickName;
    /**
    * 会员性别：1、男；2、女
    */
    private Integer pcSex;
    /**
    * 生日
    */
    private String pcBirthday;
    /**
    * 有效状态:1正常0无效
    */
    private Integer enableStatus;
    /**
    * 删除标记:0未删除1已删除
    */
    private Integer delFlag;
    /**
    * 创建人
    */
    private Long createUser;
    /**
    * 修改人
    */
    private Long modifyUser;

}