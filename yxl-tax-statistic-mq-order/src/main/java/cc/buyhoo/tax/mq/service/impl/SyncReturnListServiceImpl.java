package cc.buyhoo.tax.mq.service.impl;

import cc.buyhoo.tax.mq.dao.BusReturnListDetailMapper;
import cc.buyhoo.tax.mq.dao.BusReturnListMapper;
import cc.buyhoo.tax.mq.dao.BusReturnListPaydetailMapper;
import cc.buyhoo.tax.mq.entity.*;
import cc.buyhoo.tax.mq.params.ReturnListDetailParams;
import cc.buyhoo.tax.mq.params.ReturnListParams;
import cc.buyhoo.tax.mq.params.ReturnListPayDetailParams;
import cc.buyhoo.tax.mq.params.ReturnListSubscribeParams;
import cc.buyhoo.tax.mq.service.BusShopService;
import cc.buyhoo.tax.mq.service.SyncReturnListService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * @ClassName SyncRetSaleListServiceImpl
 * <AUTHOR>
 * @Date 2024/9/7 9:53
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class SyncReturnListServiceImpl implements SyncReturnListService {
    private final BusShopService busShopService;
    private final BusReturnListMapper busReturnListMapper;
    private final BusReturnListDetailMapper busReturnListDetailMapper;
    private final BusReturnListPaydetailMapper busReturnListPaydetailMapper;

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void syncReturnList(ReturnListSubscribeParams params) {
        try {
            if (ObjectUtil.isNull(params)) {
                log.error("------[MQ退款订单同步]----零售退款订单同步异常，退款订单数据为空----------");
                return;
            }
            ReturnListParams returnListParams = params.getReturnListParams();
            if (ObjectUtil.isNull(returnListParams)) {
                log.error("------[MQ退款订单同步]----零售退款订单同步异常，退款订单数据为空----------");
                return;
            }
            BusReturnListEntity returnList = new BusReturnListEntity();
            BeanUtil.copyProperties(returnListParams, returnList);
            returnList.setShopUnique(Long.parseLong(returnListParams.getShopUnique()));
            BusShopEntity shopEntity = busShopService.selectByShopUnique(Long.parseLong(returnListParams.getShopUnique()));
            if (ObjectUtil.isNull(shopEntity)) {
                return;
            }

            /*LambdaQueryWrapper<BusReturnListEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BusReturnListEntity::getSaleListUnique, returnListParams.getSaleListUnique());
            queryWrapper.eq(BusReturnListEntity::getRetListDatetime, returnListParams.getRetListDatetime());
            List<BusReturnListEntity> busReturnListEntities = busReturnListMapper.selectList(queryWrapper);
            if (ObjectUtil.isNotEmpty(busReturnListEntities)) {
                return;
            }*/

            returnList.setCompanyId(shopEntity.getCompanyId());
            returnList.setShopName(shopEntity.getShopName());

            List<ReturnListPayDetailParams> payDetailParamsList = params.getPayDetailParamsList();
            List<BusReturnListPaydetailEntity> paydetailEntityList = payDetailParamsList.stream().map(v -> {
                BusReturnListPaydetailEntity entity = new BusReturnListPaydetailEntity();
                BeanUtil.copyProperties(v, entity);
                entity.setCompanyId(returnList.getCompanyId());
                return entity;
            }).collect(Collectors.toList());

            BigDecimal serviceFee = paydetailEntityList.stream().filter(p -> p.getServiceType() != 1).map(BusReturnListPaydetailEntity::getPayMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal serviceFeeRate = BigDecimal.ZERO;
            if (BigDecimal.ZERO.compareTo(shopEntity.getServiceFeeRate()) == -1) {
                serviceFeeRate = NumberUtil.div(shopEntity.getServiceFeeRate(), BigDecimal.valueOf(100));
            }
            serviceFee = NumberUtil.mul(serviceFee, serviceFeeRate).setScale(2, BigDecimal.ROUND_HALF_UP);
            returnList.setReturnSaleListServiceFee(serviceFee);

            List<ReturnListDetailParams> saleListDetailParamsList = params.getDetailParamsList();
            List<BusReturnListDetailEntity> listDetailEntityList = saleListDetailParamsList.stream().map(v -> {
                BusReturnListDetailEntity entity = new BusReturnListDetailEntity();
                BeanUtil.copyProperties(v, entity);
                entity.setCompanyId(returnList.getCompanyId());
                return entity;
            }).collect(Collectors.toList());

            /*保存退款单相关*/
            if (ObjectUtil.isNotEmpty(returnList)) {
                busReturnListMapper.insert(returnList);
            }
            if (ObjectUtil.isNotEmpty(listDetailEntityList)) {
                busReturnListDetailMapper.insertBatch(listDetailEntityList);
            }
            if (ObjectUtil.isNotEmpty(paydetailEntityList)) {
                busReturnListPaydetailMapper.insertBatch(paydetailEntityList);
            }
        } catch (Exception e) {
            log.error("----[MQ退款订单同步]----保存退款订单异常：{}-----", e);
        }
    }
}
