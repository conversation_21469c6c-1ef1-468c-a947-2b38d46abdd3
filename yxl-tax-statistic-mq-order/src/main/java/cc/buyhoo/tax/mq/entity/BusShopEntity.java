package cc.buyhoo.tax.mq.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 商家信息
 * <AUTHOR>
 * @ClassName BusShop
 * @Date 2023-07-26
 **/

@Data
@TableName("bus_shop")
public class BusShopEntity extends BaseEntity {

	/**
	 * 商家唯一标识
	 */
	private Long shopUnique;

	/**
	 * 商家名称
	 */
	private String shopName;

	/**
	 * 店铺类型:1、便利店；2、水果店；3、母婴店；4、益农中心站；5、益农标准站、简易站、专业站；6宁宇总店、7宁宇分店、8五金店 9学校、10、机关食堂0：其他 11:加油站；12、餐饮店；13、拼团店统一社会信用代码
	 */
	private Integer shopType;

	/**
	 * 详细地址
	 */
	private String address;

	/**
	 * 联系电话
	 */
	private String shopPhone;

	/**
	 * 区县编码
	 */
	private String countyCode;

	/**
	 * 村编码
	 */
	private String townCode;

	/**
	 * 有效状态:1正常0无效
	 */
	private Integer enableStatus;

	/**
	 * 企业ID
	 */
	private Long companyId;

	/**
	 * 子账簿账号
	 */
	private String subAccNo;

	/**
	 * 合作商成本占比
	 */
	private BigDecimal cooperatorCostProportion;

	/**
	 * 供货商编号
	 */
	private String supplierNo;

	/**
	 * 邀请码
	 */
	private String invitationCode;

	/**
	 * 银行名称
	 */
	private String bankName;
	/**
	 * 银行开户行所在地（非标准银联卡必传）
	 */
	private String bankCity;
	/**
	 * 银行卡号
	 */
	private String bankCard;

	/**
	 * 法人姓名
	 */
	private String legalPerson;

	/**
	 * 法人手机号
	 */
	private String legalPhone;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 绑定状态:0未绑定1已绑定`
	 */
	private Integer bindStatus;

	/**
	 * 合作方式
	 */
	private Integer cooperateType;

	/**
	 * 该企业在招商银行内，绑定到影响力下的子商户号，用于打款
	 */
	private String subAccount;
	/**
	 * 结算服务费率（千分比）‰
	 */
	private BigDecimal serviceFeeRate;

	/**
	 * 是否有服务费：0-无，1-有
	 */
	private Integer hasServiceFee;

	/**
	 * 是否同步餐饮订单: 0-否，1-是
	 */
	private Integer syncCanyinData;

	/**
	 * 是否同步零售订单: 0-否，1-是
	 */
	private Integer syncBuyhooData;

	/**
	 * 合同附件地址
	 */
	private String contractUrl;

	/**
	 * 删除标记:0未删除1已删除
	 */
	private Integer delFlag;

	/**
	 * 创建人
	 */
	private Long createUser;
	/**
	 * 修改人
	 */
	private Long modifyUser;
}