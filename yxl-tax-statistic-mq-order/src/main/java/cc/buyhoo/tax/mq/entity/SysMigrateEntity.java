package cc.buyhoo.tax.mq.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 
 * @Description 迁入迁出管理
 * @ClassName SysMigrate
 * @Date 2024-08-29
 **/
@EqualsAndHashCode(callSuper=false)
@Data
@TableName("sys_migrate")
public class SysMigrateEntity extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 6939354634998703035L;
	/**
	* 迁入企业ID
	*/
	private Long inCompanyId;

	/**
	 * 迁出企业ID
	 */
	private Long outCompanyId;

	/**
	* 商户编码
	*/
	private Long shopUnique;

	/**
	* 审核状态：0-待审核，1-通过，2-不通过
	*/
	private Integer auditStatus;

	/**
	* 审核备注
	*/
	private String auditContent;

	/**
	* 审核人
	*/
	private Long auditUser;

	/**
	* 审核时间
	*/
	private Date auditTime;

	/**
	* 备注
	*/
	private String remark;

	/**
	* 有效状态:1正常0无效
	*/
	private Integer enableStatus;

	/**
	* 创建人
	*/
	private Long createUser;

	/**
	* 修改人
	*/
	private Long modifyUser;

	/**
	* 修改时间
	*/
	private Date modifyTime;

	/**
	* 删除标记:0未删除1已删除
	*/
	private Integer delFlag;

}