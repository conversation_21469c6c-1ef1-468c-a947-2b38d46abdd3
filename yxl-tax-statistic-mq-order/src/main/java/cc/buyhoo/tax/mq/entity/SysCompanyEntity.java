package cc.buyhoo.tax.mq.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description 公司信息
 * @ClassName SysCompany
 * @Date 2024-08-24
 **/
@EqualsAndHashCode(callSuper=false)
@Data
@TableName("sys_company")
public class SysCompanyEntity extends BaseEntity {

    /**
     * 父公司id
     */
    private Long parentCompanyId;

    /**
     * 公司类型:1系统平台2总公司3分公司
     */
    private Integer companyType;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 统一社会信用代码
     */
    private String licenseNumber;

    /**
     * 所属部门
     */
    private Long deptId;

    /**
     * 公司地址
     */
    private String address;

    /**
     * 联系人
     */
    private String contactName;

    /**
     * 联系电话
     */
    private String contactMobile;

    /**
     * 有效状态:1正常0无效
     */
    private Integer enableStatus;

    /**
     * 邀请码
     */
    private String invitationCode;

    /**
     * 开户行
     */
    private String bankName;

    /**
     * 银行账号
     */
    private String bankCard;

    /**
     * 预计纳税目标金额(元)
     */
    private BigDecimal targetAmount;

    /**
     * 纳统类型,1-按月,2-按季度,3-按年
     */
    private String taxType;

    /**
     * 未申请订单是否可以开票：0-否1-是
     */
    private Integer invoiceFlag;

    /**
     * 转账是否要审批:1无需审批2需审批
     */
    private Integer transferAccountAudit;

    /**
     * 交易费率（百分比）%
     */
    private BigDecimal payFeeRate;

    /**
     * 增值税率（百分比）%
     */
    private BigDecimal vatRate;

    /**
     * 所属行业ID
     */
    private Long industryId;

    /**
     * 所属市场ID
     */
    private Long marketId;

    /**
     * 突击顺序,由大到小
     */
    private Integer statisticGrade;

    /**
     * 突击金额(元)
     */
    private BigDecimal statisticAmount;

    /**
     * 突击状态：0-待突击；1-突击中；2-已突击
     */
    private Integer statisticStatus;

    /**
     * 是否统计：0-否；1-是
     */
    private Integer statisticsStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private Long createUser;


    /**
     * 修改人
     */
    private Long modifyUser;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 删除标记:0未删除1已删除
     */
    private Integer delFlag;


    /**
     * 支付中心密钥
     */
    private String payCenterSecretKey;


    /**
     * 子账簿密钥
     */
    private String subAccountSecretKey;
    /**
     * 商户d对应的银行编号
     */
    private String mchBankId;
    /**
     * 支付商户号
     */
    private String mchId;

    /**
     * 是否立即转账：1、是；2、否；
     */
    private Integer instantTransfer;

}