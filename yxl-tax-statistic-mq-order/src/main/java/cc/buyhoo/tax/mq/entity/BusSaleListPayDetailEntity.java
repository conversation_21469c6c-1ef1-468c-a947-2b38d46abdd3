package cc.buyhoo.tax.mq.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
* 
*/
@Data
@TableName(value = "sale_list_pay_detail")
public class BusSaleListPayDetailEntity extends BaseEntity {

    /**
     * 企业ID
     */
    private Long companyId;
    /**
    * 订单号
    */
    private String saleListUnique;
    /**
    * 收银支付方式：(1-现金，2-支付宝，3-微信，4-银行卡 ，5-储值卡 ,6-美团外卖,7-饿了么外卖,9-免密支付,10-积分兑换 ,11-百货豆 12-拉卡拉;13、易通支付;15 银联 16:合利宝)
    */
    private Integer payMethod;
    /**
    * 支付金额
    */
    private BigDecimal payMoney;
    /**
    * 收银现金支付服务端：(2、拉卡拉平台； 3 易通 4、微信平台；6、合利宝；7、银联开放平台)
    */
    private Integer serverType;
    /**
    * 收款店铺
    */
    private String mchId;
    /**
    * 支付时间
    */
    private Date payTime;

}