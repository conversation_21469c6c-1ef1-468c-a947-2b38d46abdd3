package cc.buyhoo.tax.mq.entity;

import cc.buyhoo.common.datasource.entity.BaseEntity;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;

/**
* 公司支付信息
*/
@Data
@TableName(value = "sys_company_pay_type")
public class SysCompanyPayTypeEntity extends BaseEntity {

    /**
    * 公司id
    */
    private Long companyId;
    /**
    * 支付商户号
    */
    private String mchId;
    /**
    * 支付密钥
    */
    private String mchKey;
    /**
    * 公众号配置ID
    */
    private String otherSetWo;
    /**
    * 其他需要配置的店铺信息
    */
    private String otherSet;
    /**
    * 支付渠道类型
    */
    private Integer payType;
    /**
     * 线下可用方式
     */
    private Integer validType;
    /**
     * 线下默认方式
     */
    private Integer defaultType;
    /**
     * 线上可用方式
     */
    private Integer appValidType;
    /**
     * 线上默认方式
     */
    private Integer appDefaultType;
    /**
    * 有效状态:1正常0无效
    */
    private Integer enableStatus;
    /**
    * 创建人
    */
    private Long createUser;
    /**
    * 修改人
    */
    private Long modifyUser;
    /**
    * 删除标记:0未删除1已删除
    */
    private Integer delFlag;

}