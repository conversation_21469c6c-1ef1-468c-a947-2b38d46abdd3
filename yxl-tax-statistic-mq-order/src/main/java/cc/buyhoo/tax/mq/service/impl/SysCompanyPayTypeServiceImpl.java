package cc.buyhoo.tax.mq.service.impl;

import cc.buyhoo.tax.mq.dao.SysCompanyPayTypeMapper;
import cc.buyhoo.tax.mq.entity.SysCompanyPayTypeEntity;
import cc.buyhoo.tax.mq.service.SysCompanyPayTypeService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName SysCompanyPayTypeServiceImpl
 * <AUTHOR>
 * @Date 2024/9/24 11:16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysCompanyPayTypeServiceImpl implements SysCompanyPayTypeService {
    private final SysCompanyPayTypeMapper sysCompanyPayTypeMapper;
    @Override
    public List<SysCompanyPayTypeEntity> selectList(LambdaQueryWrapper<SysCompanyPayTypeEntity> sysCompanyPayTypeQueryWrapper) {
        return sysCompanyPayTypeMapper.selectList(sysCompanyPayTypeQueryWrapper);
    }
}
