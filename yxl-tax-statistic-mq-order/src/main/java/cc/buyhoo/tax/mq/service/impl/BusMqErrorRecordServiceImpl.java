package cc.buyhoo.tax.mq.service.impl;

import cc.buyhoo.tax.mq.dao.BusMqErrorRecordMapper;
import lombok.AllArgsConstructor;
import cc.buyhoo.tax.mq.entity.BusMqErrorRecordEntity;
import cc.buyhoo.tax.mq.service.BusMqErrorRecordService;
import org.springframework.stereotype.Service;


/**
 * MQ订单同步异常记录
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-09-05
 */
@Service
@AllArgsConstructor
public class BusMqErrorRecordServiceImpl implements BusMqErrorRecordService {

    private final BusMqErrorRecordMapper busMqErrorRecordMapper;
    @Override
    public int insert(BusMqErrorRecordEntity entity) {
        return busMqErrorRecordMapper.insert(entity);
    }

}