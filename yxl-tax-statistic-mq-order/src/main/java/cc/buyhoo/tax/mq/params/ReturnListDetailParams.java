package cc.buyhoo.tax.mq.params;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description 退款订单明细
 * @ClassName ReturnListDetailParams
 * <AUTHOR>
 * @Date 2024/9/7 9:40
 **/
@Data
public class ReturnListDetailParams implements Serializable {
    private static final long serialVersionUID = 573231832124632218L;

    /**
     * id
     */
    private Long retListDetailId;

    /**
     * 退货单唯一标识符
     */
    private String saleListUnique;

    /**
     * 百货goods_id
     */
    private Long goodsId;

    /**
     * 商品条形码
     */
    private String goodsBarcode;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 退货数量
     */
    private BigDecimal retListDetailCount;

    /**
     * 退货价格
     */
    private BigDecimal retListDetailPrice;

    /**
     * 退回货物处理方式：1、入库；2、报损（过期产品）；3、其他
     */
    private Integer handleWay;

    /**
     * 退货商品的销售原价
     */
    private BigDecimal retListOriginPrice;

    /**
     * 退款单号
     */
    private String retListUnique;

    /**
     * 订单详情id
     */
    private Long rsaleListDetailId;
}
