package cc.buyhoo.tax.mq.enums;

import cc.buyhoo.common.enums.ErrorEnum;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum BusShopErrorEnum implements ErrorEnum {

    ID_NULL_ERROR("shop-0001","未获取到商户信息"),
    ;

    private final String code;
    private final String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
