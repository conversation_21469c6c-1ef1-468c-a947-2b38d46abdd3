package cc.buyhoo.tax.mq.service.impl;

import cc.buyhoo.tax.mq.dao.SysCompanyMapper;
import cc.buyhoo.tax.mq.dao.SysMarketMapper;
import cc.buyhoo.tax.mq.entity.SysMarketEntity;
import cc.buyhoo.tax.mq.service.SysMarketService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.Serializable;

/**
 * @ClassName SysMarketServiceImpl
 * <AUTHOR>
 * @Date 2024/9/26 11:08
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SysMarketServiceImpl implements SysMarketService {
    private final SysMarketMapper sysMarketMapper;
    @Override
    public SysMarketEntity selectById(Long id) {
        return sysMarketMapper.selectById(id);
    }
}
