package cc.buyhoo.tax.mq.service.impl;

import cc.buyhoo.common.enums.SystemErrorEnum;
import cc.buyhoo.common.standard.Result;
import cc.buyhoo.tax.facade.BusShopApi;
import cc.buyhoo.tax.facade.params.busShop.ShopPayChangeParams;
import cc.buyhoo.tax.mq.config.RabbitBuyhooConfig;
import cc.buyhoo.tax.mq.entity.BusShopEntity;
import cc.buyhoo.tax.mq.entity.SysCompanyEntity;
import cc.buyhoo.tax.mq.entity.SysCompanyPayTypeEntity;
import cc.buyhoo.tax.mq.enums.BusShopErrorEnum;
import cc.buyhoo.tax.mq.enums.SysCompanyErrorEnum;
import cc.buyhoo.tax.mq.service.BusShopService;
import cc.buyhoo.tax.mq.service.SysCompanyPayTypeService;
import cc.buyhoo.tax.mq.service.SysCompanyService;
import cc.buyhoo.tax.mq.vo.ShopPayChangeVO;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * @ClassName BusShopApiImpl
 * <AUTHOR>
 * @Date 2024/9/6 11:39
 **/
@Slf4j
@DubboService
@Service
@RequiredArgsConstructor
public class BusShopApiImpl implements BusShopApi {

    private final RabbitTemplate rabbitTemplate;
    private final SysCompanyService sysCompanyService;
    private final BusShopService busShopService;
    private final SysCompanyPayTypeService sysCompanyPayTypeService;

    @Override
    public Result<Void> shopPayChangeList(ShopPayChangeParams params) {
        SysCompanyEntity entity = sysCompanyService.selectById(params.getCompanyId());
        if (null == entity) {
            return Result.fail(SysCompanyErrorEnum.ID_NULL_ERROR);
        }
        LambdaQueryWrapper<SysCompanyPayTypeEntity> sysCompanyPayTypeQueryWrapper = new LambdaQueryWrapper<>();
        sysCompanyPayTypeQueryWrapper.eq(SysCompanyPayTypeEntity::getCompanyId, entity.getId());
        List<SysCompanyPayTypeEntity> sysCompanyPayTypeList = sysCompanyPayTypeService.selectList(sysCompanyPayTypeQueryWrapper);
        if (ObjectUtil.isEmpty(sysCompanyPayTypeList)) {
            return Result.fail(SysCompanyErrorEnum.NOT_PAY);
        }
        BusShopEntity shopEntity = busShopService.selectByShopUnique(params.getShopUnique());
        if (null == shopEntity) {
            return Result.fail(BusShopErrorEnum.ID_NULL_ERROR);
        }
        List<ShopPayChangeVO> shopPayChangeList = getShopPayChangeVOS(sysCompanyPayTypeList, shopEntity);
        if (ObjectUtil.isNotEmpty(shopPayChangeList)) {
            String body = JSONUtil.toJsonStr(shopPayChangeList);
            log.info("---[MQ修改商户支付渠道]----发送内容：{}----------", body);
            rabbitTemplate.convertAndSend(RabbitBuyhooConfig.SHOP_PAY_CHANGE_EXCHANGE, "", body);
            log.info("---[MQ修改商户支付渠道]----发送内容：{}----发送完成------", body);
        } else {
            return Result.fail(SysCompanyErrorEnum.NOT_PAY_USE);
        }
        return Result.ok();
    }

    private static List<ShopPayChangeVO> getShopPayChangeVOS(List<SysCompanyPayTypeEntity> sysCompanyPayTypeList, BusShopEntity shopEntity) {
        List<ShopPayChangeVO> shopPayChangeList = new ArrayList<>();
        for (SysCompanyPayTypeEntity sysCompanyPayTypeEntity : sysCompanyPayTypeList) {
            if (ObjectUtil.isNotNull(sysCompanyPayTypeEntity.getMchId()) && ObjectUtil.isNotNull(sysCompanyPayTypeEntity.getMchKey())) {
                ShopPayChangeVO vo = new ShopPayChangeVO();
                vo.setShopUnique(shopEntity.getShopUnique());
                vo.setMchId(sysCompanyPayTypeEntity.getMchId());
                vo.setMchKey(sysCompanyPayTypeEntity.getMchKey());
                vo.setOtherSetWo(sysCompanyPayTypeEntity.getOtherSetWo());
                vo.setOtherSet(sysCompanyPayTypeEntity.getOtherSet());
                vo.setPayType(sysCompanyPayTypeEntity.getPayType());
                vo.setValidType(sysCompanyPayTypeEntity.getValidType());
                vo.setDefaultType(sysCompanyPayTypeEntity.getDefaultType());
                vo.setAppValidType(sysCompanyPayTypeEntity.getAppValidType());
                vo.setAppDefaultType(sysCompanyPayTypeEntity.getAppDefaultType());
                shopPayChangeList.add(vo);
            }
        }
        return shopPayChangeList;
    }
}
