package cc.buyhoo.tax.mq.params;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description 退款支付明细
 * @ClassName ReturnListPayDetailParams
 * <AUTHOR>
 * @Date 2024/9/7 9:41
 **/
@Data
public class ReturnListPayDetailParams implements Serializable {
    private static final long serialVersionUID = 6162247721599964348L;

    /**
     * 订单单号
     */
    private String saleListUnique;

    /**
     * 退货单号，一个订单可退多次，也可以有几种不同的退款方式
     */
    private String retListUnique;

    /**
     * 退款的收款方式：1、现金；2、支付宝；3、微信；4、银行卡；5、储值卡；6、其他；7、优惠券；8、百货豆rn查询时，先判断service_type，在判断pay_type
     */
    private Integer payType;

    /**
     * 退款金额
     */
    private BigDecimal payMoney;

    /**
     * 现金支付服务端：1、线下操作；2、拉卡拉平台； 3 易通 4、微信平台；5、其他平台 ，6、合利宝，7、云平台
     */
    private Integer serviceType;

    /**
     * 退款的账号
     */
    private String mchId;
}
