<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cc.buyhoo.tax</groupId>
        <artifactId>yxl-statistic</artifactId>
        <version>1.0.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <groupId>cc.buyhoo.tax</groupId>
    <artifactId>yxl-tax-statistic-mq-order</artifactId>
    <version>1.0.0</version>
    <name>yxl-tax-statistic-mq-order</name>
    <description>yxl-tax-statistic-mq-order</description>
    <properties>
        <java.version>17</java.version>
        <yxl.version>2.1.0</yxl.version>
        <satoken.version>1.35.0.RC</satoken.version>
        <easyexcel.version>3.1.1</easyexcel.version>
        <tax-statistic.version>1.0.0</tax-statistic.version>
        <rocketmq.version>2.3.1</rocketmq.version>
        <nacos.version>2.1.2</nacos.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>cc.buyhoo.common</groupId>
            <artifactId>yxl-common-core</artifactId>
            <version>${yxl.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
        </dependency>
        <dependency>
            <groupId>cc.buyhoo.common</groupId>
            <artifactId>yxl-common-datasource</artifactId>
            <version>${yxl.version}</version>
        </dependency>
        <dependency>
            <groupId>cc.buyhoo.common</groupId>
            <artifactId>yxl-common-redis</artifactId>
            <version>${yxl.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
            <version>${nacos.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>nacos-client</artifactId>
                    <groupId>com.alibaba.nacos</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>nacos-client</artifactId>
                    <groupId>com.alibaba.nacos</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cc.buyhoo.common</groupId>
            <artifactId>yxl-common-web</artifactId>
            <version>${yxl.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>cc.buyhoo.common</groupId>
            <artifactId>yxl-common-dubbo</artifactId>
            <version>${yxl.version}</version>
        </dependency>
        <dependency>
            <groupId>cc.buyhoo.tax</groupId>
            <artifactId>yxl-tax-statistic-dubbo-api</artifactId>
            <version>${tax-statistic.version}</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

</project>